// account
import account from './account/account';

import systems from './account/systems';
import sysDict from './account/sysDict';
import codeGeneration from './account/codeGeneration';
import tenant from './account/tenant';
import thirdLogin from './account/thirdLogin';
import terminal from './account/terminal';
import config from './account/config';



export default {
  // system module
  account,
  systems,
  sysDict,
  codeGeneration,
  tenant,
  thirdLogin,
  config,
  terminal
};
