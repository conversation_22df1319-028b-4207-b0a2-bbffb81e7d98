{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue", "mtime": 1753863099924}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/es6.function.name\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport \"core-js/modules/es6.array.find\";\nimport \"core-js/modules/es6.array.find-index\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nexport default {\n  name: 'AdminManageDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    tenantInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    var _this = this;\n    return {\n      dialogVisible: false,\n      formDialogVisible: false,\n      tableLoading: false,\n      formLoading: false,\n      isEdit: false,\n      formTitle: '添加管理员',\n      // 分页相关数据\n      pagination: {\n        currentPage: 1,\n        pageSize: 10,\n        total: 0\n      },\n      adminList: [{\n        id: 1,\n        name: 'admin001',\n        nickname: '系统管理员',\n        mobile: '13800138001',\n        roleId: 1,\n        roleName: '超级管理员',\n        orgId: 1,\n        orgName: '总部',\n        tenantId: 1\n      }, {\n        id: 2,\n        name: 'manager001',\n        nickname: '业务经理',\n        mobile: '13800138002',\n        roleId: 2,\n        roleName: '业务管理员',\n        orgId: 2,\n        orgName: '业务部',\n        tenantId: 1\n      }, {\n        id: 3,\n        name: 'user001',\n        nickname: '普通用户1',\n        mobile: '13800138003',\n        roleId: 3,\n        roleName: '普通用户',\n        orgId: 3,\n        orgName: '技术部',\n        tenantId: 1\n      }, {\n        id: 4,\n        name: 'user002',\n        nickname: '普通用户2',\n        mobile: '13800138004',\n        roleId: 3,\n        roleName: '普通用户',\n        orgId: 1,\n        orgName: '总部',\n        tenantId: 1\n      }, {\n        id: 5,\n        name: 'admin002',\n        nickname: '系统管理员2',\n        mobile: '13800138005',\n        roleId: 1,\n        roleName: '超级管理员',\n        orgId: 2,\n        orgName: '业务部',\n        tenantId: 1\n      }, {\n        id: 6,\n        username: 'manager002',\n        nickname: '业务经理2',\n        phone: '13800138006',\n        roleId: 2,\n        roleName: '业务管理员',\n        orgId: 3,\n        orgName: '技术部',\n        tenantId: 1\n      }, {\n        id: 7,\n        username: 'user003',\n        nickname: '普通用户3',\n        phone: '13800138007',\n        roleId: 3,\n        roleName: '普通用户',\n        orgId: 1,\n        orgName: '总部',\n        tenantId: 1\n      }, {\n        id: 8,\n        username: 'user004',\n        nickname: '普通用户4',\n        phone: '13800138008',\n        roleId: 3,\n        roleName: '普通用户',\n        orgId: 2,\n        orgName: '业务部',\n        tenantId: 1\n      }, {\n        id: 9,\n        username: 'admin003',\n        nickname: '系统管理员3',\n        phone: '13800138009',\n        roleId: 1,\n        roleName: '超级管理员',\n        orgId: 3,\n        orgName: '技术部',\n        tenantId: 1\n      }, {\n        id: 10,\n        username: 'manager003',\n        nickname: '业务经理3',\n        phone: '13800138010',\n        roleId: 2,\n        roleName: '业务管理员',\n        orgId: 1,\n        orgName: '总部',\n        tenantId: 1\n      }, {\n        id: 11,\n        username: 'user005',\n        nickname: '普通用户5',\n        phone: '13800138011',\n        roleId: 3,\n        roleName: '普通用户',\n        orgId: 2,\n        orgName: '业务部',\n        tenantId: 1\n      }, {\n        id: 12,\n        username: 'user006',\n        nickname: '普通用户6',\n        phone: '***********',\n        roleId: 3,\n        roleName: '普通用户',\n        orgId: 3,\n        orgName: '技术部',\n        tenantId: 1\n      }],\n      roleList: [{\n        id: 1,\n        name: '超级管理员',\n        code: 'super_admin'\n      }, {\n        id: 2,\n        name: '业务管理员',\n        code: 'business_admin'\n      }, {\n        id: 3,\n        name: '普通用户',\n        code: 'normal_user'\n      }],\n      orgList: [{\n        id: 1,\n        name: '总部',\n        code: 'headquarters'\n      }, {\n        id: 2,\n        name: '业务部',\n        code: 'business_dept'\n      }, {\n        id: 3,\n        name: '技术部',\n        code: 'tech_dept'\n      }],\n      adminForm: {\n        id: null,\n        username: '',\n        nickname: '',\n        phone: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        orgId: null,\n        tenantId: null\n      },\n      formRules: {\n        username: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        nickname: [{\n          required: true,\n          message: '请输入昵称',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '请输入手机号',\n          trigger: 'blur'\n        }, {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: '请输入正确的手机号格式',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          message: '密码长度不能少于6位',\n          trigger: 'blur'\n        }],\n        confirmPassword: [{\n          required: true,\n          message: '请再次输入密码',\n          trigger: 'blur'\n        }, {\n          validator: function validator(rule, value, callback) {\n            if (value !== _this.adminForm.password) {\n              callback(new Error('两次输入的密码不一致'));\n            } else {\n              callback();\n            }\n          },\n          trigger: 'blur'\n        }],\n        roleId: [{\n          required: true,\n          message: '请选择角色',\n          trigger: 'change'\n        }],\n        orgId: [{\n          required: true,\n          message: '请选择组织',\n          trigger: 'change'\n        }]\n      }\n    };\n  },\n  computed: {\n    // 当前页显示的数据\n    currentPageData: function currentPageData() {\n      var start = (this.pagination.currentPage - 1) * this.pagination.pageSize;\n      var end = start + this.pagination.pageSize;\n      return this.adminList.slice(start, end);\n    }\n  },\n  watch: {\n    visible: function visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        this.loadAdminList();\n        this.loadRoleList();\n        this.loadOrgList();\n        // 更新分页总数\n        this.pagination.total = this.adminList.length;\n      }\n    },\n    dialogVisible: function dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    // 加载管理员列表\n    loadAdminList: function loadAdminList() {\n      var _this2 = this;\n      if (!this.tenantInfo.id) {\n        return;\n      }\n      this.tableLoading = true;\n      // 使用假数据模拟API调用\n      setTimeout(function () {\n        // 假数据已经在data中定义，这里不需要重新赋值\n        // 更新分页总数\n        _this2.pagination.total = _this2.adminList.length;\n        _this2.tableLoading = false;\n      }, 500);\n    },\n    // 加载角色列表\n    loadRoleList: function loadRoleList() {\n      // 使用假数据，已在data中定义\n      console.log('角色列表已加载');\n    },\n    // 加载组织列表\n    loadOrgList: function loadOrgList() {\n      // 使用假数据，已在data中定义\n      console.log('组织列表已加载');\n    },\n    // 添加管理员\n    handleAdd: function handleAdd() {\n      this.isEdit = false;\n      this.formTitle = '添加管理员';\n      this.adminForm = {\n        id: null,\n        username: '',\n        nickname: '',\n        phone: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        orgId: null,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n    // 编辑管理员\n    handleEdit: function handleEdit(row) {\n      this.isEdit = true;\n      this.formTitle = '编辑管理员';\n      this.adminForm = {\n        id: row.id,\n        username: row.username,\n        nickname: row.nickname,\n        phone: row.phone || '',\n        password: '',\n        // 编辑时不显示密码字段\n        confirmPassword: '',\n        roleId: row.roleId,\n        orgId: row.orgId,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n    // 删除管理员\n    handleDelete: function handleDelete(row) {\n      // 模拟删除操作\n      var index = this.adminList.findIndex(function (item) {\n        return item.id === row.id;\n      });\n      if (index !== -1) {\n        this.adminList.splice(index, 1);\n        this.$message.success('删除成功');\n      } else {\n        this.$message.error('删除失败');\n      }\n    },\n    // 表单提交\n    handleFormSubmit: function handleFormSubmit() {\n      var _this3 = this;\n      this.$refs.adminForm.validate(function (valid) {\n        if (valid) {\n          _this3.formLoading = true;\n\n          // 模拟API调用\n          setTimeout(function () {\n            if (_this3.isEdit) {\n              // 编辑模式：更新现有数据\n              var index = _this3.adminList.findIndex(function (item) {\n                return item.id === _this3.adminForm.id;\n              });\n              if (index !== -1) {\n                var roleInfo = _this3.roleList.find(function (role) {\n                  return role.id === _this3.adminForm.roleId;\n                });\n                var orgInfo = _this3.orgList.find(function (org) {\n                  return org.id === _this3.adminForm.orgId;\n                });\n                _this3.adminList.splice(index, 1, _objectSpread(_objectSpread({}, _this3.adminForm), {}, {\n                  roleName: roleInfo ? roleInfo.name : '',\n                  orgName: orgInfo ? orgInfo.name : ''\n                }));\n              }\n            } else {\n              // 添加模式：添加新数据\n              var _roleInfo = _this3.roleList.find(function (role) {\n                return role.id === _this3.adminForm.roleId;\n              });\n              var _orgInfo = _this3.orgList.find(function (org) {\n                return org.id === _this3.adminForm.orgId;\n              });\n              var newAdmin = _objectSpread(_objectSpread({}, _this3.adminForm), {}, {\n                id: Date.now(),\n                // 使用时间戳作为临时ID\n                roleName: _roleInfo ? _roleInfo.name : '',\n                orgName: _orgInfo ? _orgInfo.name : ''\n              });\n              _this3.adminList.push(newAdmin);\n            }\n            _this3.$message.success(_this3.isEdit ? '修改成功' : '添加成功');\n            _this3.formDialogVisible = false;\n            _this3.formLoading = false;\n          }, 500);\n        }\n      });\n    },\n    // 表单取消\n    handleFormCancel: function handleFormCancel() {\n      this.formDialogVisible = false;\n      this.$refs.adminForm.resetFields();\n    },\n    // 关闭主弹窗\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    },\n    // 分页相关方法\n    handleSizeChange: function handleSizeChange(val) {\n      this.pagination.pageSize = val;\n      this.pagination.currentPage = 1; // 重置到第一页\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.pagination.currentPage = val;\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "tenantInfo", "Object", "data", "_this", "dialogVisible", "formDialogVisible", "tableLoading", "formLoading", "isEdit", "formTitle", "pagination", "currentPage", "pageSize", "total", "adminList", "id", "nickname", "mobile", "roleId", "<PERSON><PERSON><PERSON>", "orgId", "orgName", "tenantId", "username", "phone", "roleList", "code", "orgList", "adminForm", "password", "confirmPassword", "formRules", "required", "message", "trigger", "pattern", "min", "validator", "rule", "value", "callback", "Error", "computed", "currentPageData", "start", "end", "slice", "watch", "val", "loadAdminList", "loadRoleList", "loadOrgList", "length", "$emit", "methods", "_this2", "setTimeout", "console", "log", "handleAdd", "handleEdit", "row", "handleDelete", "index", "findIndex", "item", "splice", "$message", "success", "error", "handleFormSubmit", "_this3", "$refs", "validate", "valid", "roleInfo", "find", "role", "orgInfo", "org", "_objectSpread", "newAdmin", "Date", "now", "push", "handleFormCancel", "resetFields", "handleClose", "handleSizeChange", "handleCurrentChange"], "sources": ["src/bysc_system/views/tenant/components/AdminManageDialog.vue"], "sourcesContent": ["\n<template>\n  <el-drawer\n    title=\"维护管理员\"\n    :visible.sync=\"dialogVisible\"\n    direction=\"rtl\"\n    size=\"80%\"\n    :close-on-press-escape=\"false\"\n    :wrapperClosable=\"false\"\n    @close=\"handleClose\"\n  >\n    <div class=\"admin-manage-container\">\n      <!-- 操作按钮区域 -->\n      <div class=\"action-bar\">\n        <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\">添加</el-button>\n      </div>\n\n      <!-- 管理员列表表格 -->\n      <el-table\n        :data=\"currentPageData\"\n        stripe\n        style=\"width: 100%\"\n        v-loading=\"tableLoading\"\n        height=\"400\"\n        border\n      >\n        <el-table-column\n          prop=\"name\"\n          label=\"用户名\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"nickname\"\n          label=\"昵称\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"mobile\"\n          label=\"电话\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"roleName\"\n          label=\"角色\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"orgName\"\n          label=\"组织\"\n          min-width=\"200\"\n          align=\"center\"\n        />\n        <el-table-column\n          label=\"操作\"\n          width=\"150\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"handleEdit(scope.row)\"\n            >\n              修改\n            </el-button>\n            <el-popconfirm\n              title=\"确定要删除该管理员吗？\"\n              @confirm=\"handleDelete(scope.row)\"\n            >\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                style=\"color: #f56c6c\"\n                slot=\"reference\"\n              >\n                删除\n              </el-button>\n            </el-popconfirm>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页组件 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.currentPage\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :page-size=\"pagination.pageSize\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加/编辑管理员表单弹窗 -->\n    <el-dialog\n      :title=\"formTitle\"\n      :visible.sync=\"formDialogVisible\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      append-to-body\n    >\n      <el-form\n        :model=\"adminForm\"\n        :rules=\"formRules\"\n        ref=\"adminForm\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"name\">\n          <el-input\n            v-model=\"adminForm.name\"\n            placeholder=\"请输入用户名\"\n            :disabled=\"isEdit\"\n          />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input\n            v-model=\"adminForm.nickname\"\n            placeholder=\"请输入昵称\"\n          />\n        </el-form-item>\n\n\n        <!-- 添加模式下显示的字段 -->\n        <template v-if=\"!isEdit\">\n\n            <el-form-item label=\"电话\" prop=\"mobile\">\n          <el-input\n            v-model=\"adminForm.mobile\"\n            placeholder=\"请输入电话\"\n            maxlength=\"11\"\n\n          />\n        </el-form-item>\n\n          <el-form-item label=\"密码\" prop=\"password\">\n            <el-input\n              v-model=\"adminForm.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              show-password\n            />\n          </el-form-item>\n          <el-form-item label=\"再次确认密码\" prop=\"confirmPassword\">\n            <el-input\n              v-model=\"adminForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              show-password\n            />\n          </el-form-item>\n          <el-form-item label=\"角色\" prop=\"roleId\">\n            <el-select\n              v-model=\"adminForm.roleId\"\n              placeholder=\"请选择角色\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"role in roleList\"\n                :key=\"role.id\"\n                :label=\"role.name\"\n                :value=\"role.id\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"组织\" prop=\"orgId\">\n            <el-select\n              v-model=\"adminForm.orgId\"\n              placeholder=\"请选择组织\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"org in orgList\"\n                :key=\"org.id\"\n                :label=\"org.name\"\n                :value=\"org.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </template>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleFormCancel\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleFormSubmit\"\n          :loading=\"formLoading\"\n        >\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </el-drawer>\n</template>\n\n<script>\nexport default {\n  name: 'AdminManageDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    tenantInfo: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      formDialogVisible: false,\n      tableLoading: false,\n      formLoading: false,\n      isEdit: false,\n      formTitle: '添加管理员',\n      // 分页相关数据\n      pagination: {\n        currentPage: 1,\n        pageSize: 10,\n        total: 0\n      },\n      adminList: [\n        {\n          id: 1,\n          name: 'admin001',\n          nickname: '系统管理员',\n          mobile: '13800138001',\n          roleId: 1,\n          roleName: '超级管理员',\n          orgId: 1,\n          orgName: '总部',\n          tenantId: 1\n        },\n        {\n          id: 2,\n          name: 'manager001',\n          nickname: '业务经理',\n          mobile: '13800138002',\n          roleId: 2,\n          roleName: '业务管理员',\n          orgId: 2,\n          orgName: '业务部',\n          tenantId: 1\n        },\n        {\n          id: 3,\n          name: 'user001',\n          nickname: '普通用户1',\n          mobile: '13800138003',\n          roleId: 3,\n          roleName: '普通用户',\n          orgId: 3,\n          orgName: '技术部',\n          tenantId: 1\n        },\n        {\n          id: 4,\n          name: 'user002',\n          nickname: '普通用户2',\n          mobile: '13800138004',\n          roleId: 3,\n          roleName: '普通用户',\n          orgId: 1,\n          orgName: '总部',\n          tenantId: 1\n        },\n        {\n          id: 5,\n          name: 'admin002',\n          nickname: '系统管理员2',\n          mobile: '13800138005',\n          roleId: 1,\n          roleName: '超级管理员',\n          orgId: 2,\n          orgName: '业务部',\n          tenantId: 1\n        },\n        {\n          id: 6,\n          username: 'manager002',\n          nickname: '业务经理2',\n          phone: '13800138006',\n          roleId: 2,\n          roleName: '业务管理员',\n          orgId: 3,\n          orgName: '技术部',\n          tenantId: 1\n        },\n        {\n          id: 7,\n          username: 'user003',\n          nickname: '普通用户3',\n          phone: '13800138007',\n          roleId: 3,\n          roleName: '普通用户',\n          orgId: 1,\n          orgName: '总部',\n          tenantId: 1\n        },\n        {\n          id: 8,\n          username: 'user004',\n          nickname: '普通用户4',\n          phone: '13800138008',\n          roleId: 3,\n          roleName: '普通用户',\n          orgId: 2,\n          orgName: '业务部',\n          tenantId: 1\n        },\n        {\n          id: 9,\n          username: 'admin003',\n          nickname: '系统管理员3',\n          phone: '13800138009',\n          roleId: 1,\n          roleName: '超级管理员',\n          orgId: 3,\n          orgName: '技术部',\n          tenantId: 1\n        },\n        {\n          id: 10,\n          username: 'manager003',\n          nickname: '业务经理3',\n          phone: '13800138010',\n          roleId: 2,\n          roleName: '业务管理员',\n          orgId: 1,\n          orgName: '总部',\n          tenantId: 1\n        },\n        {\n          id: 11,\n          username: 'user005',\n          nickname: '普通用户5',\n          phone: '13800138011',\n          roleId: 3,\n          roleName: '普通用户',\n          orgId: 2,\n          orgName: '业务部',\n          tenantId: 1\n        },\n        {\n          id: 12,\n          username: 'user006',\n          nickname: '普通用户6',\n          phone: '***********',\n          roleId: 3,\n          roleName: '普通用户',\n          orgId: 3,\n          orgName: '技术部',\n          tenantId: 1\n        }\n      ],\n      roleList: [\n        {\n          id: 1,\n          name: '超级管理员',\n          code: 'super_admin'\n        },\n        {\n          id: 2,\n          name: '业务管理员',\n          code: 'business_admin'\n        },\n        {\n          id: 3,\n          name: '普通用户',\n          code: 'normal_user'\n        }\n      ],\n      orgList: [\n        {\n          id: 1,\n          name: '总部',\n          code: 'headquarters'\n        },\n        {\n          id: 2,\n          name: '业务部',\n          code: 'business_dept'\n        },\n        {\n          id: 3,\n          name: '技术部',\n          code: 'tech_dept'\n        }\n      ],\n      adminForm: {\n        id: null,\n        username: '',\n        nickname: '',\n        phone: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        orgId: null,\n        tenantId: null\n      },\n      formRules: {\n        username: [\n          {required: true, message: '请输入用户名', trigger: 'blur'}\n        ],\n        nickname: [\n          {required: true, message: '请输入昵称', trigger: 'blur'}\n        ],\n        phone: [\n          {required: true, message: '请输入手机号', trigger: 'blur'},\n          {\n            pattern: /^1[3-9]\\d{9}$/,\n            message: '请输入正确的手机号格式',\n            trigger: 'blur'\n          }\n        ],\n        password: [\n          {required: true, message: '请输入密码', trigger: 'blur'},\n          {min: 6, message: '密码长度不能少于6位', trigger: 'blur'}\n        ],\n        confirmPassword: [\n          {required: true, message: '请再次输入密码', trigger: 'blur'},\n          {\n            validator: (rule, value, callback) => {\n              if (value !== this.adminForm.password) {\n                callback(new Error('两次输入的密码不一致'));\n              } else {\n                callback();\n              }\n            },\n            trigger: 'blur'\n          }\n        ],\n        roleId: [\n          {required: true, message: '请选择角色', trigger: 'change'}\n        ],\n        orgId: [\n          {required: true, message: '请选择组织', trigger: 'change'}\n        ]\n      }\n    };\n  },\n  computed: {\n    // 当前页显示的数据\n    currentPageData() {\n      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;\n      const end = start + this.pagination.pageSize;\n      return this.adminList.slice(start, end);\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        this.loadAdminList();\n        this.loadRoleList();\n        this.loadOrgList();\n        // 更新分页总数\n        this.pagination.total = this.adminList.length;\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    // 加载管理员列表\n    loadAdminList() {\n      if (!this.tenantInfo.id) {\n        return;\n      }\n\n      this.tableLoading = true;\n      // 使用假数据模拟API调用\n      setTimeout(() => {\n        // 假数据已经在data中定义，这里不需要重新赋值\n        // 更新分页总数\n        this.pagination.total = this.adminList.length;\n        this.tableLoading = false;\n      }, 500);\n    },\n\n    // 加载角色列表\n    loadRoleList() {\n      // 使用假数据，已在data中定义\n      console.log('角色列表已加载');\n    },\n\n    // 加载组织列表\n    loadOrgList() {\n      // 使用假数据，已在data中定义\n      console.log('组织列表已加载');\n    },\n\n    // 添加管理员\n    handleAdd() {\n      this.isEdit = false;\n      this.formTitle = '添加管理员';\n      this.adminForm = {\n        id: null,\n        username: '',\n        nickname: '',\n        phone: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        orgId: null,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 编辑管理员\n    handleEdit(row) {\n      this.isEdit = true;\n      this.formTitle = '编辑管理员';\n      this.adminForm = {\n        id: row.id,\n        username: row.username,\n        nickname: row.nickname,\n        phone: row.phone || '',\n        password: '', // 编辑时不显示密码字段\n        confirmPassword: '',\n        roleId: row.roleId,\n        orgId: row.orgId,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 删除管理员\n    handleDelete(row) {\n      // 模拟删除操作\n      const index = this.adminList.findIndex(item => item.id === row.id);\n      if (index !== -1) {\n        this.adminList.splice(index, 1);\n        this.$message.success('删除成功');\n      } else {\n        this.$message.error('删除失败');\n      }\n    },\n\n    // 表单提交\n    handleFormSubmit() {\n      this.$refs.adminForm.validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n\n          // 模拟API调用\n          setTimeout(() => {\n            if (this.isEdit) {\n              // 编辑模式：更新现有数据\n              const index = this.adminList.findIndex(item => item.id === this.adminForm.id);\n              if (index !== -1) {\n                const roleInfo = this.roleList.find(role => role.id === this.adminForm.roleId);\n                const orgInfo = this.orgList.find(org => org.id === this.adminForm.orgId);\n\n                this.adminList.splice(index, 1, {\n                  ...this.adminForm,\n                  roleName: roleInfo ? roleInfo.name : '',\n                  orgName: orgInfo ? orgInfo.name : ''\n                });\n              }\n            } else {\n              // 添加模式：添加新数据\n              const roleInfo = this.roleList.find(role => role.id === this.adminForm.roleId);\n              const orgInfo = this.orgList.find(org => org.id === this.adminForm.orgId);\n\n              const newAdmin = {\n                ...this.adminForm,\n                id: Date.now(), // 使用时间戳作为临时ID\n                roleName: roleInfo ? roleInfo.name : '',\n                orgName: orgInfo ? orgInfo.name : ''\n              };\n              this.adminList.push(newAdmin);\n            }\n\n            this.$message.success(this.isEdit ? '修改成功' : '添加成功');\n            this.formDialogVisible = false;\n            this.formLoading = false;\n          }, 500);\n        }\n      });\n    },\n\n    // 表单取消\n    handleFormCancel() {\n      this.formDialogVisible = false;\n      this.$refs.adminForm.resetFields();\n    },\n\n    // 关闭主弹窗\n    handleClose() {\n      this.dialogVisible = false;\n    },\n\n    // 分页相关方法\n    handleSizeChange(val) {\n      this.pagination.pageSize = val;\n      this.pagination.currentPage = 1; // 重置到第一页\n    },\n\n    handleCurrentChange(val) {\n      this.pagination.currentPage = val;\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.admin-manage-container {\n  padding: 20px;\n  height: calc(100vh - 120px); // 最大化利用屏幕高度\n  display: flex;\n  flex-direction: column;\n\n  .action-bar {\n    margin-bottom: 16px;\n    padding-bottom: 16px;\n    border-bottom: 1px solid #ebeef5;\n    flex-shrink: 0; // 不缩放\n  }\n\n  .el-table {\n    border: 1px solid #ebeef5;\n    flex: 1; // 占据剩余空间\n    overflow: auto;\n  }\n\n  .pagination-container {\n    margin-top: 16px;\n    padding-top: 16px;\n    border-top: 1px solid #ebeef5;\n    text-align: right;\n    flex-shrink: 0; // 不缩放\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 16px;\n  border-top: 1px solid #ebeef5;\n}\n\n// 抽屉样式优化\n:deep(.el-drawer) {\n  .el-drawer__header {\n    padding: 20px 20px 0 20px;\n    margin-bottom: 0;\n  }\n\n  .el-drawer__body {\n    padding: 0;\n    overflow-y: auto;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;AA0MA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,aAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,WAAA;MACAC,MAAA;MACAC,SAAA;MACA;MACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,SAAA,GACA;QACAC,EAAA;QACArB,IAAA;QACAsB,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACArB,IAAA;QACAsB,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACArB,IAAA;QACAsB,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACArB,IAAA;QACAsB,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACArB,IAAA;QACAsB,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAP,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAN,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;MACA,EACA;MACAG,QAAA,GACA;QACAV,EAAA;QACArB,IAAA;QACAgC,IAAA;MACA,GACA;QACAX,EAAA;QACArB,IAAA;QACAgC,IAAA;MACA,GACA;QACAX,EAAA;QACArB,IAAA;QACAgC,IAAA;MACA,EACA;MACAC,OAAA,GACA;QACAZ,EAAA;QACArB,IAAA;QACAgC,IAAA;MACA,GACA;QACAX,EAAA;QACArB,IAAA;QACAgC,IAAA;MACA,GACA;QACAX,EAAA;QACArB,IAAA;QACAgC,IAAA;MACA,EACA;MACAE,SAAA;QACAb,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAK,QAAA;QACAC,eAAA;QACAZ,MAAA;QACAE,KAAA;QACAE,QAAA;MACA;MACAS,SAAA;QACAR,QAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,QAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,KAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAC,OAAA;UACAF,OAAA;UACAC,OAAA;QACA,EACA;QACAL,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,eAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAG,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,IAAAD,KAAA,KAAApC,KAAA,CAAAyB,SAAA,CAAAC,QAAA;cACAW,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;UACAN,OAAA;QACA,EACA;QACAhB,MAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,KAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAQ,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,IAAAC,KAAA,SAAAlC,UAAA,CAAAC,WAAA,aAAAD,UAAA,CAAAE,QAAA;MACA,IAAAiC,GAAA,GAAAD,KAAA,QAAAlC,UAAA,CAAAE,QAAA;MACA,YAAAE,SAAA,CAAAgC,KAAA,CAAAF,KAAA,EAAAC,GAAA;IACA;EACA;EACAE,KAAA;IACAnD,OAAA,WAAAA,QAAAoD,GAAA;MACA,KAAA5C,aAAA,GAAA4C,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,aAAA;QACA,KAAAC,YAAA;QACA,KAAAC,WAAA;QACA;QACA,KAAAzC,UAAA,CAAAG,KAAA,QAAAC,SAAA,CAAAsC,MAAA;MACA;IACA;IACAhD,aAAA,WAAAA,cAAA4C,GAAA;MACA,KAAAK,KAAA,mBAAAL,GAAA;IACA;EACA;EACAM,OAAA;IACA;IACAL,aAAA,WAAAA,cAAA;MAAA,IAAAM,MAAA;MACA,UAAAvD,UAAA,CAAAe,EAAA;QACA;MACA;MAEA,KAAAT,YAAA;MACA;MACAkD,UAAA;QACA;QACA;QACAD,MAAA,CAAA7C,UAAA,CAAAG,KAAA,GAAA0C,MAAA,CAAAzC,SAAA,CAAAsC,MAAA;QACAG,MAAA,CAAAjD,YAAA;MACA;IACA;IAEA;IACA4C,YAAA,WAAAA,aAAA;MACA;MACAO,OAAA,CAAAC,GAAA;IACA;IAEA;IACAP,WAAA,WAAAA,YAAA;MACA;MACAM,OAAA,CAAAC,GAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAnD,MAAA;MACA,KAAAC,SAAA;MACA,KAAAmB,SAAA;QACAb,EAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,KAAA;QACAK,QAAA;QACAC,eAAA;QACAZ,MAAA;QACAE,KAAA;QACAE,QAAA,OAAAtB,UAAA,CAAAe;MACA;MACA,KAAAV,iBAAA;IACA;IAEA;IACAuD,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAArD,MAAA;MACA,KAAAC,SAAA;MACA,KAAAmB,SAAA;QACAb,EAAA,EAAA8C,GAAA,CAAA9C,EAAA;QACAQ,QAAA,EAAAsC,GAAA,CAAAtC,QAAA;QACAP,QAAA,EAAA6C,GAAA,CAAA7C,QAAA;QACAQ,KAAA,EAAAqC,GAAA,CAAArC,KAAA;QACAK,QAAA;QAAA;QACAC,eAAA;QACAZ,MAAA,EAAA2C,GAAA,CAAA3C,MAAA;QACAE,KAAA,EAAAyC,GAAA,CAAAzC,KAAA;QACAE,QAAA,OAAAtB,UAAA,CAAAe;MACA;MACA,KAAAV,iBAAA;IACA;IAEA;IACAyD,YAAA,WAAAA,aAAAD,GAAA;MACA;MACA,IAAAE,KAAA,QAAAjD,SAAA,CAAAkD,SAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlD,EAAA,KAAA8C,GAAA,CAAA9C,EAAA;MAAA;MACA,IAAAgD,KAAA;QACA,KAAAjD,SAAA,CAAAoD,MAAA,CAAAH,KAAA;QACA,KAAAI,QAAA,CAAAC,OAAA;MACA;QACA,KAAAD,QAAA,CAAAE,KAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5C,SAAA,CAAA6C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAhE,WAAA;;UAEA;UACAiD,UAAA;YACA,IAAAe,MAAA,CAAA/D,MAAA;cACA;cACA,IAAAuD,KAAA,GAAAQ,MAAA,CAAAzD,SAAA,CAAAkD,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAlD,EAAA,KAAAwD,MAAA,CAAA3C,SAAA,CAAAb,EAAA;cAAA;cACA,IAAAgD,KAAA;gBACA,IAAAY,QAAA,GAAAJ,MAAA,CAAA9C,QAAA,CAAAmD,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAA9D,EAAA,KAAAwD,MAAA,CAAA3C,SAAA,CAAAV,MAAA;gBAAA;gBACA,IAAA4D,OAAA,GAAAP,MAAA,CAAA5C,OAAA,CAAAiD,IAAA,WAAAG,GAAA;kBAAA,OAAAA,GAAA,CAAAhE,EAAA,KAAAwD,MAAA,CAAA3C,SAAA,CAAAR,KAAA;gBAAA;gBAEAmD,MAAA,CAAAzD,SAAA,CAAAoD,MAAA,CAAAH,KAAA,KAAAiB,aAAA,CAAAA,aAAA,KACAT,MAAA,CAAA3C,SAAA;kBACAT,QAAA,EAAAwD,QAAA,GAAAA,QAAA,CAAAjF,IAAA;kBACA2B,OAAA,EAAAyD,OAAA,GAAAA,OAAA,CAAApF,IAAA;gBAAA,EACA;cACA;YACA;cACA;cACA,IAAAiF,SAAA,GAAAJ,MAAA,CAAA9C,QAAA,CAAAmD,IAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAA9D,EAAA,KAAAwD,MAAA,CAAA3C,SAAA,CAAAV,MAAA;cAAA;cACA,IAAA4D,QAAA,GAAAP,MAAA,CAAA5C,OAAA,CAAAiD,IAAA,WAAAG,GAAA;gBAAA,OAAAA,GAAA,CAAAhE,EAAA,KAAAwD,MAAA,CAAA3C,SAAA,CAAAR,KAAA;cAAA;cAEA,IAAA6D,QAAA,GAAAD,aAAA,CAAAA,aAAA,KACAT,MAAA,CAAA3C,SAAA;gBACAb,EAAA,EAAAmE,IAAA,CAAAC,GAAA;gBAAA;gBACAhE,QAAA,EAAAwD,SAAA,GAAAA,SAAA,CAAAjF,IAAA;gBACA2B,OAAA,EAAAyD,QAAA,GAAAA,QAAA,CAAApF,IAAA;cAAA,EACA;cACA6E,MAAA,CAAAzD,SAAA,CAAAsE,IAAA,CAAAH,QAAA;YACA;YAEAV,MAAA,CAAAJ,QAAA,CAAAC,OAAA,CAAAG,MAAA,CAAA/D,MAAA;YACA+D,MAAA,CAAAlE,iBAAA;YACAkE,MAAA,CAAAhE,WAAA;UACA;QACA;MACA;IACA;IAEA;IACA8E,gBAAA,WAAAA,iBAAA;MACA,KAAAhF,iBAAA;MACA,KAAAmE,KAAA,CAAA5C,SAAA,CAAA0D,WAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAnF,aAAA;IACA;IAEA;IACAoF,gBAAA,WAAAA,iBAAAxC,GAAA;MACA,KAAAtC,UAAA,CAAAE,QAAA,GAAAoC,GAAA;MACA,KAAAtC,UAAA,CAAAC,WAAA;IACA;IAEA8E,mBAAA,WAAAA,oBAAAzC,GAAA;MACA,KAAAtC,UAAA,CAAAC,WAAA,GAAAqC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}