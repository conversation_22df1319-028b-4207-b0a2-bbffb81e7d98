{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\TenantForm.vue?vue&type=template&id=a220f67a&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\TenantForm.vue", "mtime": 1753782036906}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"tenant-form\"\n  }, [_c(\"el-form\", {\n    ref: \"tenantForm\",\n    staticClass: \"tenant-form-content\",\n    attrs: {\n      model: _vm.formData,\n      rules: _vm.rules,\n      \"label-width\": \"120px\"\n    }\n  }, [_c(\"el-form-item\", {\n    attrs: {\n      label: \"租户编码：\",\n      prop: \"tenantCode\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      value: _vm.formData.tenantCode,\n      placeholder: \"请输入租户编码\",\n      maxlength: \"32\",\n      disabled: _vm.isEdit\n    },\n    on: {\n      input: function input($event) {\n        _vm.handleFieldChange(\"tenantCode\", $event.trim());\n      }\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"*租户名称：\",\n      prop: \"tenantName\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      value: _vm.formData.tenantName,\n      placeholder: \"请输入租户名称\",\n      maxlength: \"50\"\n    },\n    on: {\n      input: function input($event) {\n        _vm.handleFieldChange(\"tenantName\", $event.trim());\n      }\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"租户管理员：\",\n      prop: \"tenantAdmin\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      value: _vm.formData.tenantAdmin,\n      placeholder: \"请输入租户管理员\",\n      maxlength: \"50\"\n    },\n    on: {\n      input: function input($event) {\n        _vm.handleFieldChange(\"tenantAdmin\", $event.trim());\n      }\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"租户备注：\",\n      prop: \"comments\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      value: _vm.formData.comments,\n      type: \"textarea\",\n      rows: 4,\n      placeholder: \"请输入租户备注\",\n      maxlength: \"200\",\n      \"show-word-limit\": \"\"\n    },\n    on: {\n      input: function input($event) {\n        _vm.handleFieldChange(\"comments\", $event.trim());\n      }\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"是否选中：\",\n      prop: \"isSelected\"\n    }\n  }, [_c(\"el-switch\", {\n    attrs: {\n      value: _vm.formData.isSelected,\n      \"active-text\": \"是\",\n      \"inactive-text\": \"否\"\n    },\n    on: {\n      change: function change($event) {\n        return _vm.handleFieldChange(\"isSelected\", $event);\n      }\n    }\n  })], 1)], 1), _c(\"div\", {\n    staticClass: \"form-actions\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      loading: _vm.loading\n    },\n    on: {\n      click: _vm.handleSubmit\n    }\n  }, [_vm._v(\"\\n      \" + _vm._s(_vm.isEdit ? \"保存\" : \"添加\") + \"\\n    \")]), _c(\"el-button\", {\n    on: {\n      click: _vm.handleCancel\n    }\n  }, [_vm._v(\"取消\")])], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "ref", "attrs", "model", "formData", "rules", "label", "prop", "value", "tenantCode", "placeholder", "maxlength", "disabled", "isEdit", "on", "input", "$event", "handleFieldChange", "trim", "tenantName", "tenantAdmin", "comments", "type", "rows", "isSelected", "change", "loading", "click", "handleSubmit", "_v", "_s", "handleCancel", "staticRenderFns", "_withStripped"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/views/terminalAssignment/components/TenantForm.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"tenant-form\" },\n    [\n      _c(\n        \"el-form\",\n        {\n          ref: \"tenantForm\",\n          staticClass: \"tenant-form-content\",\n          attrs: {\n            model: _vm.formData,\n            rules: _vm.rules,\n            \"label-width\": \"120px\",\n          },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"租户编码：\", prop: \"tenantCode\" } },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  value: _vm.formData.tenantCode,\n                  placeholder: \"请输入租户编码\",\n                  maxlength: \"32\",\n                  disabled: _vm.isEdit,\n                },\n                on: {\n                  input: function ($event) {\n                    _vm.handleFieldChange(\"tenantCode\", $event.trim())\n                  },\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"*租户名称：\", prop: \"tenantName\" } },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  value: _vm.formData.tenantName,\n                  placeholder: \"请输入租户名称\",\n                  maxlength: \"50\",\n                },\n                on: {\n                  input: function ($event) {\n                    _vm.handleFieldChange(\"tenantName\", $event.trim())\n                  },\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"租户管理员：\", prop: \"tenantAdmin\" } },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  value: _vm.formData.tenantAdmin,\n                  placeholder: \"请输入租户管理员\",\n                  maxlength: \"50\",\n                },\n                on: {\n                  input: function ($event) {\n                    _vm.handleFieldChange(\"tenantAdmin\", $event.trim())\n                  },\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"租户备注：\", prop: \"comments\" } },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  value: _vm.formData.comments,\n                  type: \"textarea\",\n                  rows: 4,\n                  placeholder: \"请输入租户备注\",\n                  maxlength: \"200\",\n                  \"show-word-limit\": \"\",\n                },\n                on: {\n                  input: function ($event) {\n                    _vm.handleFieldChange(\"comments\", $event.trim())\n                  },\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"是否选中：\", prop: \"isSelected\" } },\n            [\n              _c(\"el-switch\", {\n                attrs: {\n                  value: _vm.formData.isSelected,\n                  \"active-text\": \"是\",\n                  \"inactive-text\": \"否\",\n                },\n                on: {\n                  change: function ($event) {\n                    return _vm.handleFieldChange(\"isSelected\", $event)\n                  },\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"form-actions\" },\n        [\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"primary\", loading: _vm.loading },\n              on: { click: _vm.handleSubmit },\n            },\n            [\n              _vm._v(\n                \"\\n      \" + _vm._s(_vm.isEdit ? \"保存\" : \"添加\") + \"\\n    \"\n              ),\n            ]\n          ),\n          _c(\"el-button\", { on: { click: _vm.handleCancel } }, [\n            _vm._v(\"取消\"),\n          ]),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,SAAS,EACT;IACEG,GAAG,EAAE,YAAY;IACjBD,WAAW,EAAE,qBAAqB;IAClCE,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACO,QAAQ;MACnBC,KAAK,EAAER,GAAG,CAACQ,KAAK;MAChB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACEP,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAEI,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAa;EAAE,CAAC,EACjD,CACET,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLM,KAAK,EAAEX,GAAG,CAACO,QAAQ,CAACK,UAAU;MAC9BC,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAEf,GAAG,CAACgB;IAChB,CAAC;IACDC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACoB,iBAAiB,CAAC,YAAY,EAAED,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;MACpD;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAEI,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAa;EAAE,CAAC,EAClD,CACET,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLM,KAAK,EAAEX,GAAG,CAACO,QAAQ,CAACe,UAAU;MAC9BT,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE;IACb,CAAC;IACDG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACoB,iBAAiB,CAAC,YAAY,EAAED,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;MACpD;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAEI,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAc;EAAE,CAAC,EACnD,CACET,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLM,KAAK,EAAEX,GAAG,CAACO,QAAQ,CAACgB,WAAW;MAC/BV,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;IACb,CAAC;IACDG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACoB,iBAAiB,CAAC,aAAa,EAAED,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;MACrD;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAEI,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAW;EAAE,CAAC,EAC/C,CACET,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLM,KAAK,EAAEX,GAAG,CAACO,QAAQ,CAACiB,QAAQ;MAC5BC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,CAAC;MACPb,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE,KAAK;MAChB,iBAAiB,EAAE;IACrB,CAAC;IACDG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACoB,iBAAiB,CAAC,UAAU,EAAED,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;MAClD;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAEI,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAa;EAAE,CAAC,EACjD,CACET,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MACLM,KAAK,EAAEX,GAAG,CAACO,QAAQ,CAACoB,UAAU;MAC9B,aAAa,EAAE,GAAG;MAClB,eAAe,EAAE;IACnB,CAAC;IACDV,EAAE,EAAE;MACFW,MAAM,EAAE,SAARA,MAAMA,CAAYT,MAAM,EAAE;QACxB,OAAOnB,GAAG,CAACoB,iBAAiB,CAAC,YAAY,EAAED,MAAM,CAAC;MACpD;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDlB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEoB,IAAI,EAAE,SAAS;MAAEI,OAAO,EAAE7B,GAAG,CAAC6B;IAAQ,CAAC;IAChDZ,EAAE,EAAE;MAAEa,KAAK,EAAE9B,GAAG,CAAC+B;IAAa;EAChC,CAAC,EACD,CACE/B,GAAG,CAACgC,EAAE,CACJ,UAAU,GAAGhC,GAAG,CAACiC,EAAE,CAACjC,GAAG,CAACgB,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,QAClD,CAAC,CAEL,CAAC,EACDf,EAAE,CAAC,WAAW,EAAE;IAAEgB,EAAE,EAAE;MAAEa,KAAK,EAAE9B,GAAG,CAACkC;IAAa;EAAE,CAAC,EAAE,CACnDlC,GAAG,CAACgC,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIG,eAAe,GAAG,EAAE;AACxBpC,MAAM,CAACqC,aAAa,GAAG,IAAI;AAE3B,SAASrC,MAAM,EAAEoC,eAAe", "ignoreList": []}]}