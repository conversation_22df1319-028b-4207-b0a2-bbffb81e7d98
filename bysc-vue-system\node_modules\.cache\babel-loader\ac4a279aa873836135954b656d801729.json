{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\index.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["// account\nimport account from \"./account/account\";\nimport systems from \"./account/systems\";\nimport sysDict from \"./account/sysDict\";\nimport codeGeneration from \"./account/codeGeneration\";\nimport tenant from \"./account/tenant\";\nimport thirdLogin from \"./account/thirdLogin\";\nimport terminal from \"./account/terminal\";\nimport config from \"./account/config\";\nexport default {\n  // system module\n  account: account,\n  systems: systems,\n  sysDict: sysDict,\n  codeGeneration: codeGeneration,\n  tenant: tenant,\n  thirdLogin: thirdLogin,\n  config: config,\n  terminal: terminal\n};", {"version": 3, "names": ["account", "systems", "sysDict", "codeGeneration", "tenant", "third<PERSON><PERSON><PERSON>", "terminal", "config"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/index.js"], "sourcesContent": ["// account\r\nimport account from './account/account';\r\n\r\nimport systems from './account/systems';\r\nimport sysDict from './account/sysDict';\r\nimport codeGeneration from './account/codeGeneration';\r\nimport tenant from './account/tenant';\r\nimport thirdLogin from './account/thirdLogin';\r\nimport terminal from './account/terminal';\r\nimport config from './account/config';\r\n\r\n\r\n\r\nexport default {\r\n  // system module\r\n  account,\r\n  systems,\r\n  sysDict,\r\n  codeGeneration,\r\n  tenant,\r\n  thirdLogin,\r\n  config,\r\n  terminal\r\n};\r\n"], "mappings": "AAAA;AACA,OAAOA,OAAO;AAEd,OAAOC,OAAO;AACd,OAAOC,OAAO;AACd,OAAOC,cAAc;AACrB,OAAOC,MAAM;AACb,OAAOC,UAAU;AACjB,OAAOC,QAAQ;AACf,OAAOC,MAAM;AAIb,eAAe;EACb;EACAP,OAAO,EAAPA,OAAO;EACPC,OAAO,EAAPA,OAAO;EACPC,OAAO,EAAPA,OAAO;EACPC,cAAc,EAAdA,cAAc;EACdC,MAAM,EAANA,MAAM;EACNC,UAAU,EAAVA,UAAU;EACVE,MAAM,EAANA,MAAM;EACND,QAAQ,EAARA;AACF,CAAC", "ignoreList": []}]}