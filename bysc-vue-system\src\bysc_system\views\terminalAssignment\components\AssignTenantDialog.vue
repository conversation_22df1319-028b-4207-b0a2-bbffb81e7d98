<template>
  <el-drawer
    title="分配租户"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="500px"
    :close-on-press-escape="false"
    :modal-append-to-body="false"
    @close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="assignForm" label-width="100px">
      <el-form-item label="证件柜ID">
        <el-input v-model="form.icbId" disabled></el-input>
      </el-form-item>
      <el-form-item label="证件柜名称">
        <el-input v-model="form.icbName" disabled></el-input>
      </el-form-item>
      <el-form-item label="选择租户" prop="tenantId">
        <el-select
          v-model="form.tenantId"
          placeholder="请选择租户"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="tenant in tenantList"
            :key="tenant.id"
            :label="tenant.tenantName"
            :value="tenant.id">
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 注意提示 -->
      <el-alert
        title="注意：更改租户后，该设备本地数据（用户、班组、策略）将会被清空，需要到新租户上进行重新配置操作！"
        type="warning"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;">
      </el-alert>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'AssignTenantDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    terminalInfo: {
      type: Object,
      default: () => ({})
    },
    tenantList: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        icbId: '',
        icbName: '',
        tenantId: ''
      },
      rules: {
        tenantId: [
          {required: true, message: '请选择租户', trigger: 'change'}
        ]
      }
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    },
    terminalInfo: {
      handler(val) {
        if (val && this.visible) {
          this.initForm();
        }
      },
      deep: true
    }
  },
  methods: {
    initForm() {
      this.form = {
        icbId: this.terminalInfo.icbId || '',
        icbName: this.terminalInfo.icbName || '',
        tenantId: ''
      };
      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.assignForm) {
          this.$refs.assignForm.clearValidate();
        }
      });
    },

    handleConfirm() {
      this.$refs.assignForm.validate(valid => {
        if (valid) {
          const data = {
            icbId: this.form.icbId,
            tenantId: this.form.tenantId
          };
          this.$emit('confirm', data);
        }
      });
    },

    handleCancel() {
      this.drawerVisible = false;
    },

    handleClose() {
      this.form = {
        icbId: '',
        icbName: '',
        tenantId: ''
      };
      if (this.$refs.assignForm) {
        this.$refs.assignForm.clearValidate();
      }
      this.$emit('close');
    }
  }
};
</script>

<style lang="less" scoped>
.drawer-footer {
  text-align: right;
  padding: 20px;
  border-top: 1px solid #e8e8e8;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
}

::v-deep .el-drawer__body {
  padding-bottom: 80px;
}

::v-deep .el-alert__content {
  line-height: 1.5;
}
</style>
