<!--
 * @Author: czw
 * @Date: 2024-01-01 00:00:00
 * @LastEditors: czw
 * @LastEditTime: 2024-01-01 00:00:00
 * @Description: 租户表单组件
 *
 * Copyright (c) 2024 by czw/bysc, All Rights Reserved.
-->
<template>
  <div class="tenant-form">
    <el-form
      :model="formData"
      :rules="rules"
      ref="tenantForm"
      label-width="120px"
      class="tenant-form-content"
    >
      <el-form-item label="租户编码：" prop="tenantCode">
        <el-input
          v-model.trim="formData.tenantCode"
          placeholder="请输入租户编码"
          maxlength="32"
          :disabled="isEdit"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="*租户名称：" prop="tenantName">
        <el-input
          v-model.trim="formData.tenantName"
          placeholder="请输入租户名称"
          maxlength="50"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="联系人：" prop="tenantAdmin">
        <el-input
          v-model.trim="formData.tenantAdmin"
          placeholder="请输入联系人"
          maxlength="50"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="租户备注：" prop="comments">
        <el-input
          v-model.trim="formData.comments"
          type="textarea"
          :rows="4"
          placeholder="请输入租户备注"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="是否选中：" prop="isSelected">
        <el-switch
          v-model="formData.isSelected"
          active-text="是"
          inactive-text="否"
        ></el-switch>
      </el-form-item>
    </el-form>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '保存' : '添加' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TenantForm',
  props: {
    // 表单数据
    value: {
      type: Object,
      default: () => ({
        id: null,
        tenantCode: '',
        tenantName: '',
        tenantAdmin: '',
        comments: '',
        isSelected: false
      })
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 提交时的loading状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        id: null,
        tenantCode: '',
        tenantName: '',
        tenantAdmin: '',
        comments: '',
        isSelected: false
      },
      rules: {
        tenantCode: [
          { required: true, message: '请输入租户编码', trigger: 'blur' },
          { min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur' }
        ],
        tenantName: [
          { required: true, message: '请输入租户名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        tenantAdmin: [
          { min: 0, max: 50, message: '长度不能超过 50 个字符', trigger: 'blur' }
        ],
        comments: [
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.formData = { ...newVal }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 提交表单
    handleSubmit() {
      this.$refs.tenantForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.formData)
        } else {
          return false
        }
      })
    },
    
    // 取消操作
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 重置表单
    resetForm() {
      this.$refs.tenantForm.resetFields()
      this.formData = {
        id: null,
        tenantCode: '',
        tenantName: '',
        tenantAdmin: '',
        comments: '',
        isSelected: false
      }
    },
    
    // 验证表单
    validate() {
      return new Promise((resolve) => {
        this.$refs.tenantForm.validate((valid) => {
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.tenant-form {
  padding: 20px;
  
  .tenant-form-content {
    .el-form-item {
      margin-bottom: 20px;
      
      .el-input,
      .el-textarea {
        width: 100%;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    margin-top: 30px;
    
    .el-button {
      margin: 0 10px;
      min-width: 80px;
    }
  }
}
</style>
