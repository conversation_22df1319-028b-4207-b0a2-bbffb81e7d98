{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue?vue&type=template&id=60b06436&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue", "mtime": 1753863128961}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<el-drawer\n  title=\"维护管理员\"\n  :visible.sync=\"dialogVisible\"\n  direction=\"rtl\"\n  size=\"80%\"\n  :close-on-press-escape=\"false\"\n  :wrapperClosable=\"false\"\n  @close=\"handleClose\"\n>\n  <div class=\"admin-manage-container\">\n    <!-- 操作按钮区域 -->\n    <div class=\"action-bar\">\n      <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\">添加</el-button>\n    </div>\n\n    <!-- 管理员列表表格 -->\n    <el-table\n      :data=\"currentPageData\"\n      stripe\n      style=\"width: 100%\"\n      v-loading=\"tableLoading\"\n      height=\"400\"\n      border\n    >\n      <el-table-column\n        prop=\"name\"\n        label=\"用户名\"\n        width=\"150\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"nickname\"\n        label=\"昵称\"\n        width=\"150\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"mobile\"\n        label=\"电话\"\n        width=\"150\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"roleName\"\n        label=\"角色\"\n        width=\"150\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"orgName\"\n        label=\"组织\"\n        min-width=\"200\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"操作\"\n        width=\"150\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            @click=\"handleEdit(scope.row)\"\n          >\n            修改\n          </el-button>\n          <el-popconfirm\n            title=\"确定要删除该管理员吗？\"\n            @confirm=\"handleDelete(scope.row)\"\n          >\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              style=\"color: #f56c6c\"\n              slot=\"reference\"\n            >\n              删除\n            </el-button>\n          </el-popconfirm>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页组件 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"pagination.currentPage\"\n        :page-sizes=\"[10, 20, 50, 100]\"\n        :page-size=\"pagination.pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"pagination.total\"\n      />\n    </div>\n  </div>\n\n  <!-- 添加/编辑管理员表单弹窗 -->\n  <el-dialog\n    :title=\"formTitle\"\n    :visible.sync=\"formDialogVisible\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n    append-to-body\n  >\n    <el-form\n      :model=\"adminForm\"\n      :rules=\"formRules\"\n      ref=\"adminForm\"\n      label-width=\"80px\"\n    >\n      <el-form-item label=\"用户名\" prop=\"name\">\n        <el-input\n          v-model=\"adminForm.name\"\n          placeholder=\"请输入用户名\"\n          :disabled=\"isEdit\"\n        />\n      </el-form-item>\n      <el-form-item label=\"昵称\" prop=\"nickname\">\n        <el-input\n          v-model=\"adminForm.nickname\"\n          placeholder=\"请输入昵称\"\n        />\n      </el-form-item>\n\n\n      <!-- 添加模式下显示的字段 -->\n      <template v-if=\"!isEdit\">\n\n          <el-form-item label=\"电话\" prop=\"mobile\">\n        <el-input\n          v-model=\"adminForm.mobile\"\n          placeholder=\"请输入电话\"\n          maxlength=\"11\"\n\n        />\n      </el-form-item>\n\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input\n            v-model=\"adminForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            show-password\n          />\n        </el-form-item>\n        <el-form-item label=\"再次确认密码\" prop=\"confirmPassword\">\n          <el-input\n            v-model=\"adminForm.confirmPassword\"\n            type=\"password\"\n            placeholder=\"请再次输入密码\"\n            show-password\n          />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"roleId\">\n          <el-select\n            v-model=\"adminForm.roleId\"\n            placeholder=\"请选择角色\"\n            style=\"width: 100%\"\n          >\n            <el-option\n              v-for=\"role in roleList\"\n              :key=\"role.id\"\n              :label=\"role.name\"\n              :value=\"role.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"组织\" prop=\"orgId\">\n          <el-select\n            v-model=\"adminForm.orgId\"\n            placeholder=\"请选择组织\"\n            style=\"width: 100%\"\n          >\n            <el-option\n              v-for=\"org in orgList\"\n              :key=\"org.id\"\n              :label=\"org.name\"\n              :value=\"org.id\"\n            />\n          </el-select>\n        </el-form-item>\n      </template>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleFormCancel\">取消</el-button>\n      <el-button\n        type=\"primary\"\n        @click=\"handleFormSubmit\"\n        :loading=\"formLoading\"\n      >\n        确定\n      </el-button>\n    </div>\n  </el-dialog>\n</el-drawer>\n", null]}