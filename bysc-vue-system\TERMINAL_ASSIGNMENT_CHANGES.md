# 终端分配页面修改说明

## 修改概述
根据原型图要求，将 `src/bysc_system/views/terminalAssignment/index.vue` 从租户管理页面重构为终端分配页面。

## 主要修改内容

### 1. 搜索表单字段更新
- **终端ID**: 用于搜索特定终端
- **终端IP**: 用于搜索终端IP地址
- **终端名称**: 用于搜索终端名称
- **状态**: 在线/离线状态筛选
- **分配状态**: 已分配/待分配状态筛选

### 2. 表格列字段更新
根据原型图，表格包含以下列：
- **序号**: 自动编号
- **终端ID**: 显示终端唯一标识
- **终端IP**: 显示终端IP地址
- **终端名称**: 显示终端名称
- **终端上线时间**: 显示终端最后上线时间
- **状态**: 显示在线/离线状态（带颜色标签）
- **分配状态**: 显示已分配/待分配状态（带颜色标签）
- **当前租户**: 显示当前分配的租户名称
- **操作**: 根据分配状态显示不同操作按钮

### 3. 操作功能
- **分配租户**: 当终端状态为"待分配"时显示，用于为终端分配租户
- **更改租户**: 当终端状态为"已分配"时显示，用于更改终端的租户

### 4. 抽屉功能
- 移除了原有的租户管理相关弹窗
- 使用抽屉（Drawer）组件替代弹窗，提供更好的用户体验
- **分配租户抽屉**（AssignTenantDialog.vue）：
  - 终端ID（只读）
  - 终端名称（只读）
  - 租户选择下拉框（支持搜索）
  - 警告提示信息
- **更改租户抽屉**（ChangeTenantDialog.vue）：
  - 终端ID（只读）
  - 终端名称（只读）
  - 当前租户（只读）
  - 租户选择下拉框（过滤当前租户，支持搜索）
  - 二次确认弹窗
  - 警告提示信息

### 5. API接口更新
- 主要数据接口: `terminal/terminal-page`
- 租户列表接口: `tenant/tenant-list`
- 分配租户接口: `terminal/assign-tenant`

### 6. 数据结构
```javascript
// 搜索表单
searchForm: {
  terminalId: '',
  terminalIp: '',
  terminalName: '',
  status: '',        // 'online' | 'offline'
  assignStatus: ''   // 'assigned' | 'pending'
}

// 分配表单
assignForm: {
  terminalId: '',
  terminalName: '',
  tenantId: ''
}
```

## 状态标签说明
- **在线状态**: 绿色标签显示"在线"，红色标签显示"离线"
- **分配状态**: 绿色标签显示"已分配"，橙色标签显示"待分配"

## 组件结构
```
src/bysc_system/views/terminalAssignment/
├── index.vue                           # 主页面
└── components/
    ├── AssignTenantDialog.vue          # 分配租户抽屉组件
    └── ChangeTenantDialog.vue          # 更改租户抽屉组件
```

## 注意事项
1. 页面已移除所有租户管理相关的功能和组件
2. 保留了原有的Grid组件和LoadingFix工具
3. 操作按钮根据终端的分配状态动态显示
4. 使用抽屉（Drawer）组件替代弹窗，提供更好的用户体验
5. 抽屉组件支持从右侧滑出，宽度为500px
6. 更改租户功能包含二次确认机制
7. 所有API接口需要后端配合实现

## 测试验证
- 项目可以正常编译和启动
- 页面结构符合原型图要求
- 所有交互功能已实现（需要后端API支持）
