{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\ChangeTenantDialog.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\ChangeTenantDialog.vue", "mtime": 1753846764174}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./ChangeTenantDialog.vue?vue&type=template&id=34c21397&scoped=true\"\nimport script from \"./ChangeTenantDialog.vue?vue&type=script&lang=js\"\nexport * from \"./ChangeTenantDialog.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChangeTenantDialog.vue?vue&type=style&index=0&id=34c21397&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"34c21397\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('34c21397')) {\n      api.createRecord('34c21397', component.options)\n    } else {\n      api.reload('34c21397', component.options)\n    }\n    module.hot.accept(\"./ChangeTenantDialog.vue?vue&type=template&id=34c21397&scoped=true\", function () {\n      api.rerender('34c21397', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/bysc_system/views/terminalAssignment/components/ChangeTenantDialog.vue\"\nexport default component.exports"]}