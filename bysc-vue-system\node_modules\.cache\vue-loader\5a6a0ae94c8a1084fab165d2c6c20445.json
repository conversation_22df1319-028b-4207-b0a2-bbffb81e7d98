{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\ChangeTenantDialog.vue?vue&type=style&index=0&id=34c21397&lang=less&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\ChangeTenantDialog.vue", "mtime": 1753846764174}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\css-loader\\index.js", "mtime": 1745221300128}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745221314654}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745221303798}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745221307121}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n.drawer-footer {\n  text-align: right;\n  padding: 20px;\n  border-top: 1px solid #e8e8e8;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n\n::v-deep .el-drawer__body {\n  padding-bottom: 80px;\n}\n\n::v-deep .el-alert__content {\n  line-height: 1.5;\n}\n", {"version": 3, "sources": ["ChangeTenantDialog.vue"], "names": [], "mappings": ";AAsMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "ChangeTenantDialog.vue", "sourceRoot": "src/bysc_system/views/terminalAssignment/components", "sourcesContent": ["<template>\n  <el-drawer\n    title=\"更改租户\"\n    :visible.sync=\"drawerVisible\"\n    direction=\"rtl\"\n    size=\"500px\"\n    :close-on-press-escape=\"false\"\n    :modal-append-to-body=\"false\"\n    @close=\"handleClose\"\n  >\n    <el-form :model=\"form\" :rules=\"rules\" ref=\"changeForm\" label-width=\"100px\">\n      <el-form-item label=\"证件柜ID\">\n        <el-input v-model=\"form.icbId\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"证件柜名称\">\n        <el-input v-model=\"form.icbName\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"当前租户\">\n        <el-input v-model=\"form.currentTenant\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"选择租户\" prop=\"tenantId\">\n        <el-select \n          v-model=\"form.tenantId\" \n          placeholder=\"支持搜索，不能选择当前租户\" \n          style=\"width: 100%\"\n          filterable\n        >\n          <el-option\n            v-for=\"tenant in availableTenants\"\n            :key=\"tenant.id\"\n            :label=\"tenant.tenantName\"\n            :value=\"tenant.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      \n      <!-- 注意提示 -->\n      <el-alert\n        title=\"注意：更改租户后，该设备本地数据（用户、班组、策略）将会被清空，需要到新租户上进行重新配置操作！\"\n        type=\"warning\"\n        :closable=\"false\"\n        show-icon\n        style=\"margin-bottom: 20px;\">\n      </el-alert>\n    </el-form>\n    \n    <div class=\"drawer-footer\">\n      <el-button @click=\"handleCancel\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\" :loading=\"loading\">确 定</el-button>\n    </div>\n\n    <!-- 再次确认弹窗 -->\n    <el-dialog\n      title=\"再次确认更改\"\n      :visible.sync=\"confirmDialogVisible\"\n      width=\"400px\"\n      :close-on-click-modal=\"false\"\n      append-to-body\n    >\n      <div style=\"text-align: center; padding: 20px 0;\">\n        <p style=\"font-size: 16px; color: #E6A23C; margin-bottom: 20px;\">\n          请再次确认是否要进行租户绑定\n        </p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"confirmDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleFinalConfirm\" :loading=\"loading\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </el-drawer>\n</template>\n\n<script>\nexport default {\n  name: 'ChangeTenantDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    terminalInfo: {\n      type: Object,\n      default: () => ({})\n    },\n    tenantList: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      form: {\n        icbId: '',\n        icbName: '',\n        currentTenant: '',\n        tenantId: ''\n      },\n      rules: {\n        tenantId: [\n          { required: true, message: '请选择租户', trigger: 'change' }\n        ]\n      },\n      confirmDialogVisible: false\n    };\n  },\n  computed: {\n    drawerVisible: {\n      get() {\n        return this.visible;\n      },\n      set(val) {\n        this.$emit('update:visible', val);\n      }\n    },\n    // 过滤掉当前租户，不能选择当前租户\n    availableTenants() {\n      return this.tenantList.filter(tenant =>\n        tenant.id !== this.terminalInfo.distributeTenantId\n      );\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initForm();\n      } else {\n        this.confirmDialogVisible = false;\n      }\n    },\n    terminalInfo: {\n      handler(val) {\n        if (val && this.visible) {\n          this.initForm();\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initForm() {\n      this.form = {\n        icbId: this.terminalInfo.icbId || '',\n        icbName: this.terminalInfo.icbName || '',\n        currentTenant: this.terminalInfo.currentTenant || '',\n        tenantId: ''\n      };\n      // 清除验证\n      this.$nextTick(() => {\n        if (this.$refs.changeForm) {\n          this.$refs.changeForm.clearValidate();\n        }\n      });\n    },\n    \n    handleConfirm() {\n      this.$refs.changeForm.validate((valid) => {\n        if (valid) {\n          // 显示再次确认弹窗\n          this.confirmDialogVisible = true;\n        }\n      });\n    },\n    \n    handleFinalConfirm() {\n      const data = {\n        icbId: this.form.icbId,\n        tenantId: this.form.tenantId\n      };\n      this.confirmDialogVisible = false;\n      this.$emit('confirm', data);\n    },\n    \n    handleCancel() {\n      this.drawerVisible = false;\n    },\n    \n    handleClose() {\n      this.form = {\n        icbId: '',\n        icbName: '',\n        currentTenant: '',\n        tenantId: ''\n      };\n      this.confirmDialogVisible = false;\n      if (this.$refs.changeForm) {\n        this.$refs.changeForm.clearValidate();\n      }\n      this.$emit('close');\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.drawer-footer {\n  text-align: right;\n  padding: 20px;\n  border-top: 1px solid #e8e8e8;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n\n::v-deep .el-drawer__body {\n  padding-bottom: 80px;\n}\n\n::v-deep .el-alert__content {\n  line-height: 1.5;\n}\n</style>\n"]}]}