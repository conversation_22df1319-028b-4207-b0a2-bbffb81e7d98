{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\TenantForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\TenantForm.vue", "mtime": 1753782036906}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\nexport default {\n  name: 'TenantForm',\n  props: {\n    // 表单数据\n    value: {\n      type: Object,\n      default: () => ({\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      })\n    },\n    // 是否为编辑模式\n    isEdit: {\n      type: Boolean,\n      default: false\n    },\n    // 提交时的loading状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formData: {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      },\n      rules: {\n        tenantCode: [\n          {required: true, message: '请输入租户编码', trigger: 'blur'},\n          {min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur'}\n        ],\n        tenantName: [\n          {required: true, message: '请输入租户名称', trigger: 'blur'},\n          {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}\n        ],\n        tenantAdmin: [\n          {min: 0, max: 50, message: '长度不能超过 50 个字符', trigger: 'blur'}\n        ],\n        comments: [\n          {max: 200, message: '长度不能超过 200 个字符', trigger: 'blur'}\n        ]\n      },\n      updateTimer: null // 用于防抖的定时器\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal, oldVal) {\n        // 避免不必要的更新，只在值真正改变时才更新\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          this.formData = {...newVal};\n        }\n      },\n      immediate: true,\n      deep: false // 改为浅层监听，提高性能\n    }\n  },\n  methods: {\n    // 处理表单字段变化，使用防抖优化性能\n    handleFieldChange(field, value) {\n      // 使用 Vue.set 确保响应式更新\n      this.$set(this.formData, field, value);\n\n      // 防抖发送更新事件，避免频繁触发\n      if (this.updateTimer) {\n        clearTimeout(this.updateTimer);\n      }\n      this.updateTimer = setTimeout(() => {\n        this.$emit('input', {...this.formData});\n      }, 100);\n    },\n\n    // 提交表单\n    handleSubmit() {\n      this.$refs.tenantForm.validate(valid => {\n        if (valid) {\n          this.$emit('submit', this.formData);\n        } else {\n          return false;\n        }\n      });\n    },\n\n    // 取消操作\n    handleCancel() {\n      this.$emit('cancel');\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.tenantForm.resetFields();\n      this.formData = {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      };\n    },\n\n    // 验证表单\n    validate() {\n      return new Promise(resolve => {\n        this.$refs.tenantForm.validate(valid => {\n          resolve(valid);\n        });\n      });\n    }\n  },\n\n  beforeDestroy() {\n    // 清理定时器，防止内存泄漏\n    if (this.updateTimer) {\n      clearTimeout(this.updateTimer);\n    }\n  }\n};\n", {"version": 3, "sources": ["TenantForm.vue"], "names": [], "mappings": ";AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TenantForm.vue", "sourceRoot": "src/bysc_system/views/terminalAssignment/components", "sourcesContent": ["<!--\n * @Author: czw\n * @Date: 2024-01-01 00:00:00\n * @LastEditors: czw\n * @LastEditTime: 2024-01-01 00:00:00\n * @Description: 租户表单组件\n *\n * Copyright (c) 2024 by czw/bysc, All Rights Reserved.\n-->\n<template>\n  <div class=\"tenant-form\">\n    <el-form\n      :model=\"formData\"\n      :rules=\"rules\"\n      ref=\"tenantForm\"\n      label-width=\"120px\"\n      class=\"tenant-form-content\"\n    >\n      <el-form-item label=\"租户编码：\" prop=\"tenantCode\">\n        <el-input\n          :value=\"formData.tenantCode\"\n          @input=\"handleFieldChange('tenantCode', $event.trim())\"\n          placeholder=\"请输入租户编码\"\n          maxlength=\"32\"\n          :disabled=\"isEdit\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"*租户名称：\" prop=\"tenantName\">\n        <el-input\n          :value=\"formData.tenantName\"\n          @input=\"handleFieldChange('tenantName', $event.trim())\"\n          placeholder=\"请输入租户名称\"\n          maxlength=\"50\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"租户管理员：\" prop=\"tenantAdmin\">\n        <el-input\n          :value=\"formData.tenantAdmin\"\n          @input=\"handleFieldChange('tenantAdmin', $event.trim())\"\n          placeholder=\"请输入租户管理员\"\n          maxlength=\"50\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"租户备注：\" prop=\"comments\">\n        <el-input\n          :value=\"formData.comments\"\n          @input=\"handleFieldChange('comments', $event.trim())\"\n          type=\"textarea\"\n          :rows=\"4\"\n          placeholder=\"请输入租户备注\"\n          maxlength=\"200\"\n          show-word-limit\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"是否选中：\" prop=\"isSelected\">\n        <el-switch\n          :value=\"formData.isSelected\"\n          @change=\"handleFieldChange('isSelected', $event)\"\n          active-text=\"是\"\n          inactive-text=\"否\"\n        ></el-switch>\n      </el-form-item>\n    </el-form>\n\n    <div class=\"form-actions\">\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">\n        {{ isEdit ? '保存' : '添加' }}\n      </el-button>\n      <el-button @click=\"handleCancel\">取消</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TenantForm',\n  props: {\n    // 表单数据\n    value: {\n      type: Object,\n      default: () => ({\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      })\n    },\n    // 是否为编辑模式\n    isEdit: {\n      type: Boolean,\n      default: false\n    },\n    // 提交时的loading状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formData: {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      },\n      rules: {\n        tenantCode: [\n          {required: true, message: '请输入租户编码', trigger: 'blur'},\n          {min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur'}\n        ],\n        tenantName: [\n          {required: true, message: '请输入租户名称', trigger: 'blur'},\n          {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}\n        ],\n        tenantAdmin: [\n          {min: 0, max: 50, message: '长度不能超过 50 个字符', trigger: 'blur'}\n        ],\n        comments: [\n          {max: 200, message: '长度不能超过 200 个字符', trigger: 'blur'}\n        ]\n      },\n      updateTimer: null // 用于防抖的定时器\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal, oldVal) {\n        // 避免不必要的更新，只在值真正改变时才更新\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          this.formData = {...newVal};\n        }\n      },\n      immediate: true,\n      deep: false // 改为浅层监听，提高性能\n    }\n  },\n  methods: {\n    // 处理表单字段变化，使用防抖优化性能\n    handleFieldChange(field, value) {\n      // 使用 Vue.set 确保响应式更新\n      this.$set(this.formData, field, value);\n\n      // 防抖发送更新事件，避免频繁触发\n      if (this.updateTimer) {\n        clearTimeout(this.updateTimer);\n      }\n      this.updateTimer = setTimeout(() => {\n        this.$emit('input', {...this.formData});\n      }, 100);\n    },\n\n    // 提交表单\n    handleSubmit() {\n      this.$refs.tenantForm.validate(valid => {\n        if (valid) {\n          this.$emit('submit', this.formData);\n        } else {\n          return false;\n        }\n      });\n    },\n\n    // 取消操作\n    handleCancel() {\n      this.$emit('cancel');\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.tenantForm.resetFields();\n      this.formData = {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      };\n    },\n\n    // 验证表单\n    validate() {\n      return new Promise(resolve => {\n        this.$refs.tenantForm.validate(valid => {\n          resolve(valid);\n        });\n      });\n    }\n  },\n\n  beforeDestroy() {\n    // 清理定时器，防止内存泄漏\n    if (this.updateTimer) {\n      clearTimeout(this.updateTimer);\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.tenant-form {\n  padding: 20px;\n\n  .tenant-form-content {\n    .el-form-item {\n      margin-bottom: 20px;\n\n      .el-input,\n      .el-textarea {\n        width: 100%;\n      }\n    }\n  }\n\n  .form-actions {\n    text-align: center;\n    margin-top: 30px;\n\n    .el-button {\n      margin: 0 10px;\n      min-width: 80px;\n    }\n  }\n}\n</style>\n"]}]}