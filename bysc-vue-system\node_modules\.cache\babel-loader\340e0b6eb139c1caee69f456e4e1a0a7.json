{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue", "mtime": 1753862180628}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\nimport _typeof from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport Vue from 'vue';\nimport Grid from '@/components/Grid';\nimport TenantForm from \"./components/TenantForm.vue\";\nimport AdminManageDialog from \"./components/AdminManageDialog.vue\";\nimport LoadingFix from '@/utils/loading-fix';\nvar defaultSearchForm = {\n  tenantCode: '',\n  tenantName: '',\n  tenantAdmin: '',\n  isSelected: ''\n};\nvar defaultConfigForm = {\n  \"tenantCode\": \"\",\n  \"tenantName\": \"\",\n  // \"tenantAdmin\": \"\",\n  \"comments\": \"\",\n  \"isSelected\": 2\n};\nexport default {\n  components: {\n    Grid: Grid,\n    TenantForm: TenantForm,\n    AdminManageDialog: AdminManageDialog\n  },\n  destroyed: function destroyed() {\n    this.searchEventBus.$off();\n    this.searchConfigEventBus.$off();\n\n    // 清理遮罩层观察器\n    if (this.maskObserver) {\n      this.maskObserver.disconnect();\n    }\n  },\n  data: function data() {\n    this.searchEventBus = new Vue();\n    this.searchConfigEventBus = new Vue();\n    return {\n      configDrawer: false,\n      configDrawerName: '添加',\n      configStatus: true,\n      tenantRuleForm: Object.assign({}, defaultConfigForm),\n      isEditMode: false,\n      submitLoading: false,\n      tableLoading: false,\n      configTableData: [],\n      itemId: null,\n      configDialogName: '',\n      configDialog: false,\n      status: true,\n      rules: {\n        dictName: [{\n          required: true,\n          message: '请输入字典名称',\n          trigger: 'change,blur'\n        }],\n        dictCode: [{\n          required: true,\n          message: '请输入字典代码',\n          trigger: 'change,blur'\n        }],\n        dictOrder: [{\n          required: true,\n          message: '请输入字典排序',\n          trigger: 'change,blur'\n        }]\n      },\n      drawerName: '添加',\n      drawer: false,\n      direction: 'rtl',\n      searchForm: Object.assign({}, defaultSearchForm),\n      columns: [{\n        title: '租户编码',\n        key: 'tenantCode',\n        tooltip: true,\n        minWidth: 130\n      }, {\n        title: '租户名称',\n        key: 'tenantName',\n        tooltip: true,\n        minWidth: 130\n      }, {\n        title: '备注',\n        key: 'comments',\n        tooltip: true,\n        minWidth: 200\n      }, {\n        title: '是否选中',\n        slot: 'isSelected',\n        tooltip: true,\n        minWidth: 100\n      }],\n      tableData: [],\n      // 维护管理员相关\n      adminManageVisible: false,\n      currentTenant: {}\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    // 确保页面加载时重置所有loading状态\n    this.tableLoading = false;\n\n    // 使用 LoadingFix 工具清理遮罩层\n    this.$nextTick(function () {\n      setTimeout(function () {\n        LoadingFix.forceClearMasks();\n        _this.tableLoading = false;\n        console.log('已使用 LoadingFix 重置页面loading状态');\n      }, 100);\n    });\n\n    // 启动自动清理监听\n    this.maskObserver = LoadingFix.startAutoCleanup();\n  },\n  methods: {\n    // 获取数据时的回调\n    getDatas: function getDatas(data) {\n      this.tableData = data;\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\n    },\n    handleSelectionChange: function handleSelectionChange(row) {\n      // 处理选中状态变化\n      console.log('选中状态变化:', row.tenantName, row.isSelected);\n      // 这里可以添加API调用来保存选中状态\n      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})\n    },\n    handleAdd: function handleAdd() {\n      var _this2 = this;\n      this.drawer = true;\n      // 使用浅拷贝替代深拷贝，提高性能\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\n      this.drawerName = '新建租户';\n      this.isEditMode = false;\n\n      // 等待DOM更新后重置表单验证\n      this.$nextTick(function () {\n        if (_this2.$refs.tenantForm) {\n          _this2.$refs.tenantForm.resetForm();\n        }\n      });\n    },\n    handleEdit: function handleEdit(row) {\n      var _this3 = this;\n      // 确保 isSelected 字段的数据类型正确\n      var editData = Object.assign({}, row);\n      console.log('编辑前的原始数据:', row);\n      console.log('isSelected 原始值:', row.isSelected, '类型:', _typeof(row.isSelected));\n\n      // 如果后端返回的是 boolean 或其他类型，转换为数字\n      if (editData.isSelected === undefined || editData.isSelected === null) {\n        editData.isSelected = 2; // 默认为未选中\n      } else if (typeof editData.isSelected === 'boolean') {\n        editData.isSelected = editData.isSelected ? 1 : 2;\n      } else if (editData.isSelected === true || editData.isSelected === 'true' || editData.isSelected === 1) {\n        editData.isSelected = 1;\n      } else if (editData.isSelected === false || editData.isSelected === 'false' || editData.isSelected === 2) {\n        editData.isSelected = 2;\n      } else {\n        editData.isSelected = 2; // 其他情况默认为未选中\n      }\n      console.log('转换后的数据:', editData);\n      console.log('isSelected 转换后值:', editData.isSelected);\n      this.tenantRuleForm = editData;\n      this.drawer = true;\n      this.drawerName = '编辑租户';\n      this.isEditMode = true;\n\n      // 等待DOM更新后重置表单验证\n      this.$nextTick(function () {\n        if (_this3.$refs.tenantForm) {\n          _this3.$refs.tenantForm.resetForm();\n        }\n      });\n    },\n    handleAdminManage: function handleAdminManage(row) {\n      this.currentTenant = Object.assign({}, row);\n      this.adminManageVisible = true;\n    },\n    batchDelete: function batchDelete() {\n      var _this4 = this;\n      var ids = [];\n      this.$refs.multipleTable.selection.forEach(function (e) {\n        ids.push(e.id);\n      });\n      console.info(ids, this.$refs.multipleTable.selection);\n      if (!ids.length) {\n        this.$message.info('请先选择您要删除的项');\n        return;\n      }\n\n      // 第一次确认\n      this.$confirm(\"\\u786E\\u8BA4\\u5220\\u9664\\u9009\\u4E2D\\u7684 \".concat(ids.length, \" \\u4E2A\\u79DF\\u6237\\uFF1F\\u5220\\u9664\\u540E\\uFF0C\\u76F8\\u5173\\u79DF\\u6237\\u6570\\u636E\\u5C06\\u4F1A\\u88AB\\u6E05\\u7A7A\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\uFF01\"), '批量删除租户', {\n        confirmButtonText: '确认删除',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        // 第二次确认\n        _this4.$confirm(\"\\u786E\\u5B9A\\u6279\\u91CF\\u5220\\u9664 \".concat(ids.length, \" \\u4E2A\\u79DF\\u6237\\uFF0C\\u6CE8\\u610F\\uFF0C\\u6B64\\u6B65\\u9AA4\\u4E0D\\u53EF\\u9006\\uFF0C\\u786E\\u5B9A\\u540E\\u4F1A\\u5C06\\u8FD9\\u4E9B\\u79DF\\u6237\\u7684\\u6240\\u6709\\u6570\\u636E\\u90FD\\u6E05\\u7A7A\\uFF01\"), '再次确认', {\n          confirmButtonText: '确认删除',\n          cancelButtonText: '取消',\n          type: 'error'\n        }).then(function () {\n          // 执行删除操作\n          _this4.$api['tenant/tenant-delete']({\n            ids: ids\n          }).then(function (data) {\n            _this4.$refs.grid.query();\n            _this4.$message({\n              message: '删除成功',\n              type: 'success'\n            });\n          }).catch(function (error) {\n            _this4.$message({\n              message: '删除失败',\n              type: 'error'\n            });\n          });\n        }).catch(function () {\n          _this4.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n      }).catch(function () {\n        _this4.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    },\n    handleDelete: function handleDelete(e) {\n      var _this5 = this;\n      // 第一次确认\n      this.$confirm('确认删除该租户？删除后，相关租户数据将会被清空，请谨慎操作！', '删除租户', {\n        confirmButtonText: '确认删除',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        // 第二次确认\n        _this5.$confirm('确定删除租户，注意，此步骤不可逆，确定后会将租户所有数据都清空！', '再次确认', {\n          confirmButtonText: '确认删除',\n          cancelButtonText: '取消',\n          type: 'error'\n        }).then(function () {\n          // 执行删除操作\n          _this5.$api['tenant/tenant-delete']({\n            ids: [e]\n          }).then(function (data) {\n            _this5.$refs.grid.query();\n            _this5.$message({\n              message: '删除成功',\n              type: 'success'\n            });\n          }).catch(function (error) {\n            _this5.$message({\n              message: '删除失败',\n              type: 'error'\n            });\n          });\n        }).catch(function () {\n          _this5.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n      }).catch(function () {\n        _this5.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    },\n    searchTable: function searchTable() {\n      this.$refs.grid.query();\n    },\n    resetTable: function resetTable() {\n      var _this6 = this;\n      // 使用浅拷贝替代深拷贝，提高性能\n      this.searchForm = Object.assign({}, defaultSearchForm);\n      this.$nextTick(function () {\n        _this6.$refs.grid.query();\n      });\n    },\n    getColumn: function getColumn(e) {\n      this.columns = e;\n    },\n    handleConfigEdit: function handleConfigEdit(row) {\n      this.configStatus = !!row.dictStatus;\n      this.tenantRuleForm = Object.assign({}, row);\n      this.configDrawer = true;\n      this.configDrawerName = '编辑';\n    },\n    handleConfigDelete: function handleConfigDelete(e) {\n      var _this7 = this;\n      this.$api['sysDict/dict-item-delete']({\n        ids: [e]\n      }).then(function (data) {\n        _this7.$refs.configGrid.query();\n        _this7.$message({\n          message: '删除成功',\n          type: 'success'\n        });\n      });\n    },\n    // 表单提交处理\n    handleFormSubmit: function handleFormSubmit(formData) {\n      var _this8 = this;\n      this.submitLoading = true;\n      this.$api['tenant/tenant-save'](formData).then(function (data) {\n        _this8.$message({\n          message: '保存成功',\n          type: 'success'\n        });\n        _this8.drawer = false;\n        _this8.$refs.grid.query();\n        _this8.resetFormData();\n      }).catch(function (error) {\n        console.error('保存失败:', error);\n      }).finally(function () {\n        _this8.submitLoading = false;\n      });\n    },\n    // 表单取消处理\n    handleFormCancel: function handleFormCancel() {\n      this.drawer = false;\n      this.resetFormData();\n    },\n    // 重置表单数据\n    resetFormData: function resetFormData() {\n      // 使用浅拷贝替代深拷贝，提高性能\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\n      this.isEditMode = false;\n      this.submitLoading = false;\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "Grid", "TenantForm", "AdminManageDialog", "LoadingFix", "defaultSearchForm", "tenantCode", "tenantName", "tenantAdmin", "isSelected", "defaultConfigForm", "components", "destroyed", "searchEventBus", "$off", "searchConfigEventBus", "maskObserver", "disconnect", "data", "config<PERSON><PERSON><PERSON>", "configDrawerName", "config<PERSON><PERSON>us", "tenantRuleForm", "Object", "assign", "isEditMode", "submitLoading", "tableLoading", "configTableData", "itemId", "configDialogName", "config<PERSON><PERSON><PERSON>", "status", "rules", "dictName", "required", "message", "trigger", "dictCode", "dictOrder", "drawerName", "drawer", "direction", "searchForm", "columns", "title", "key", "tooltip", "min<PERSON><PERSON><PERSON>", "slot", "tableData", "adminManageVisible", "currentTenant", "mounted", "_this", "$nextTick", "setTimeout", "forceClearMasks", "console", "log", "startAutoCleanup", "methods", "getDatas", "handleSelectionChange", "row", "handleAdd", "_this2", "$refs", "tenantForm", "resetForm", "handleEdit", "_this3", "editData", "_typeof", "undefined", "handleAdminManage", "batchDelete", "_this4", "ids", "multipleTable", "selection", "for<PERSON>ach", "e", "push", "id", "info", "length", "$message", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "type", "then", "$api", "grid", "query", "catch", "error", "handleDelete", "_this5", "searchTable", "resetTable", "_this6", "getColumn", "handleConfigEdit", "dictStatus", "handleConfigDelete", "_this7", "config<PERSON><PERSON>", "handleFormSubmit", "formData", "_this8", "resetFormData", "finally", "handleFormCancel"], "sources": ["src/bysc_system/views/tenant/index.vue"], "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"tenant/tenant-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-form\r\n              :inline=\"true\"\r\n              :model=\"searchForm\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-form-item label=\"租户编码\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.tenantCode\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户编码\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"租户名称\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.tenantName\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户名称\"\r\n                ></el-input>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"是否选中\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.isSelected\"\r\n                  placeholder=\"请选择是否选中\"\r\n                >\r\n                  <el-option :label=\"'是'\" :value=\"1\"> </el-option>\r\n                  <el-option :label=\"'否'\" :value=\"2\"> </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  style=\"margin: 0 0 0 10px\"\r\n                  @click=\"searchTable\"\r\n                  >搜索</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <div slot=\"action\">\r\n            <el-button v-permission=\"'tenant_add'\" size=\"small\" type=\"primary\" @click=\"handleAdd\">新建租户</el-button>\r\n            <el-button v-permission=\"'tenant_batchDel'\" size=\"small\" @click=\"batchDelete\">删除租户</el-button>\r\n          </div>\r\n          <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" type=\"selection\" width=\"55\">\r\n            </el-table-column>\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'isSelected'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                 {{scope.row.isSelected === 1  ? \"是\":'否'}}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row[item.slot]?'success':'danger'\">{{scope.row[item.slot]?'启用':'禁用'}}</el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"200\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <template>\r\n                  <el-button\r\n                    v-permission=\"'tenant_edit'\"\r\n                    style=\"margin-right:6px\"\r\n                    @click=\"handleEdit(scope.row)\"\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    >编辑</el-button\r\n                  >\r\n                </template>\r\n\r\n                <template>\r\n\r\n                  <el-button\r\n                    v-permission=\"'tenant_admin_manage'\"\r\n                    style=\"margin-right:6px\"\r\n                    @click=\"handleAdminManage(scope.row)\"\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    >维护管理员</el-button\r\n                  >\r\n                </template>\r\n\r\n                <template>\r\n                  <el-button\r\n                    v-permission=\"'tenant_del'\"\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    @click=\"handleDelete(scope.row.id)\"\r\n                  >删除</el-button>\r\n                </template>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      :title=\"drawerName\"\r\n      :visible.sync=\"drawer\"\r\n      :direction=\"direction\"\r\n      :wrapperClosable=\"false\"\r\n    >\r\n      <TenantForm\r\n        v-model=\"tenantRuleForm\"\r\n        :is-edit=\"isEditMode\"\r\n        :loading=\"submitLoading\"\r\n        @submit=\"handleFormSubmit\"\r\n        @cancel=\"handleFormCancel\"\r\n        ref=\"tenantForm\"\r\n      />\r\n    </el-drawer>\r\n\r\n    <!-- 维护管理员弹窗 -->\r\n    <AdminManageDialog\r\n      :visible.sync=\"adminManageVisible\"\r\n      :tenant-info=\"currentTenant\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport TenantForm from './components/TenantForm.vue';\r\nimport AdminManageDialog from './components/AdminManageDialog.vue';\r\nimport LoadingFix from '@/utils/loading-fix';\r\nconst defaultSearchForm = {\r\n  tenantCode: '',\r\n  tenantName: '',\r\n  tenantAdmin: '',\r\n  isSelected: ''\r\n};\r\nconst defaultConfigForm = {\r\n  \"tenantCode\": \"\",\r\n  \"tenantName\": \"\",\r\n  // \"tenantAdmin\": \"\",\r\n  \"comments\": \"\",\r\n  \"isSelected\": 2\r\n};\r\nexport default {\r\n  components: {Grid, TenantForm, AdminManageDialog},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n    this.searchConfigEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    this.searchConfigEventBus = new Vue();\r\n    return {\r\n      configDrawer: false,\r\n      configDrawerName: '添加',\r\n      configStatus: true,\r\n      tenantRuleForm: Object.assign({}, defaultConfigForm),\r\n      isEditMode: false,\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      configTableData: [],\r\n      itemId: null,\r\n      configDialogName: '',\r\n      configDialog: false,\r\n      status: true,\r\n      rules: {\r\n        dictName: [\r\n          {required: true, message: '请输入字典名称', trigger: 'change,blur'}\r\n        ],\r\n        dictCode: [\r\n          {required: true, message: '请输入字典代码', trigger: 'change,blur'}\r\n        ],\r\n        dictOrder: [\r\n          {required: true, message: '请输入字典排序', trigger: 'change,blur'},\r\n        ],\r\n      },\r\n      drawerName: '添加',\r\n      drawer: false,\r\n      direction: 'rtl',\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '租户编码',\r\n          key: 'tenantCode',\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: '租户名称',\r\n          key: 'tenantName',\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n\r\n        {\r\n          title: '备注',\r\n          key: 'comments',\r\n          tooltip: true,\r\n          minWidth: 200,\r\n        },\r\n        {\r\n          title: '是否选中',\r\n          slot: 'isSelected',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 维护管理员相关\r\n      adminManageVisible: false,\r\n      currentTenant: {}\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        LoadingFix.forceClearMasks();\r\n        this.tableLoading = false;\r\n        console.log('已使用 LoadingFix 重置页面loading状态');\r\n      }, 100);\r\n    });\r\n\r\n    // 启动自动清理监听\r\n    this.maskObserver = LoadingFix.startAutoCleanup();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    handleSelectionChange(row) {\r\n      // 处理选中状态变化\r\n      console.log('选中状态变化:', row.tenantName, row.isSelected);\r\n      // 这里可以添加API调用来保存选中状态\r\n      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})\r\n    },\r\n\r\n    handleAdd() {\r\n      this.drawer = true;\r\n      // 使用浅拷贝替代深拷贝，提高性能\r\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\r\n      this.drawerName = '新建租户';\r\n      this.isEditMode = false;\r\n\r\n      // 等待DOM更新后重置表单验证\r\n      this.$nextTick(() => {\r\n        if (this.$refs.tenantForm) {\r\n          this.$refs.tenantForm.resetForm();\r\n        }\r\n      });\r\n    },\r\n    handleEdit(row) {\r\n      // 确保 isSelected 字段的数据类型正确\r\n      const editData = Object.assign({}, row);\r\n      console.log('编辑前的原始数据:', row);\r\n      console.log('isSelected 原始值:', row.isSelected, '类型:', typeof row.isSelected);\r\n\r\n      // 如果后端返回的是 boolean 或其他类型，转换为数字\r\n      if (editData.isSelected === undefined || editData.isSelected === null) {\r\n        editData.isSelected = 2; // 默认为未选中\r\n      } else if (typeof editData.isSelected === 'boolean') {\r\n        editData.isSelected = editData.isSelected ? 1 : 2;\r\n      } else if (editData.isSelected === true || editData.isSelected === 'true' || editData.isSelected === 1) {\r\n        editData.isSelected = 1;\r\n      } else if (editData.isSelected === false || editData.isSelected === 'false' || editData.isSelected === 2) {\r\n        editData.isSelected = 2;\r\n      } else {\r\n        editData.isSelected = 2; // 其他情况默认为未选中\r\n      }\r\n\r\n      console.log('转换后的数据:', editData);\r\n      console.log('isSelected 转换后值:', editData.isSelected);\r\n\r\n      this.tenantRuleForm = editData;\r\n      this.drawer = true;\r\n      this.drawerName = '编辑租户';\r\n      this.isEditMode = true;\r\n\r\n      // 等待DOM更新后重置表单验证\r\n      this.$nextTick(() => {\r\n        if (this.$refs.tenantForm) {\r\n          this.$refs.tenantForm.resetForm();\r\n        }\r\n      });\r\n    },\r\n    handleAdminManage(row) {\r\n      this.currentTenant = Object.assign({}, row);\r\n      this.adminManageVisible = true;\r\n    },\r\n    batchDelete() {\r\n      let ids = [];\r\n      this.$refs.multipleTable.selection.forEach(e => {\r\n        ids.push(e.id);\r\n      });\r\n      console.info(ids, this.$refs.multipleTable.selection);\r\n      if (!ids.length) {\r\n        this.$message.info('请先选择您要删除的项');\r\n        return;\r\n      }\r\n\r\n      // 第一次确认\r\n      this.$confirm(`确认删除选中的 ${ids.length} 个租户？删除后，相关租户数据将会被清空，请谨慎操作！`, '批量删除租户', {\r\n        confirmButtonText: '确认删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 第二次确认\r\n        this.$confirm(`确定批量删除 ${ids.length} 个租户，注意，此步骤不可逆，确定后会将这些租户的所有数据都清空！`, '再次确认', {\r\n          confirmButtonText: '确认删除',\r\n          cancelButtonText: '取消',\r\n          type: 'error'\r\n        }).then(() => {\r\n          // 执行删除操作\r\n          this.$api['tenant/tenant-delete']({ids: ids}).then(data => {\r\n            this.$refs.grid.query();\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            });\r\n          }).catch(error => {\r\n            this.$message({\r\n              message: '删除失败',\r\n              type: 'error'\r\n            });\r\n          });\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          });\r\n        });\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        });\r\n      });\r\n    },\r\n    handleDelete(e) {\r\n      // 第一次确认\r\n      this.$confirm('确认删除该租户？删除后，相关租户数据将会被清空，请谨慎操作！', '删除租户', {\r\n        confirmButtonText: '确认删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 第二次确认\r\n        this.$confirm('确定删除租户，注意，此步骤不可逆，确定后会将租户所有数据都清空！', '再次确认', {\r\n          confirmButtonText: '确认删除',\r\n          cancelButtonText: '取消',\r\n          type: 'error'\r\n        }).then(() => {\r\n          // 执行删除操作\r\n          this.$api['tenant/tenant-delete']({ids: [e]}).then(data => {\r\n            this.$refs.grid.query();\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            });\r\n          }).catch(error => {\r\n            this.$message({\r\n              message: '删除失败',\r\n              type: 'error'\r\n            });\r\n          });\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          });\r\n        });\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        });\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n    resetTable() {\r\n      // 使用浅拷贝替代深拷贝，提高性能\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    },\r\n    handleConfigEdit(row) {\r\n      this.configStatus = !!row.dictStatus;\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.configDrawer = true;\r\n      this.configDrawerName = '编辑';\r\n    },\r\n    handleConfigDelete(e) {\r\n      this.$api['sysDict/dict-item-delete']({ids: [e]}).then(data => {\r\n        this.$refs.configGrid.query();\r\n        this.$message({\r\n          message: '删除成功',\r\n          type: 'success'\r\n        });\r\n      });\r\n    },\r\n    // 表单提交处理\r\n    handleFormSubmit(formData) {\r\n      this.submitLoading = true;\r\n      this.$api['tenant/tenant-save'](formData).then(data => {\r\n        this.$message({\r\n          message: '保存成功',\r\n          type: 'success'\r\n        });\r\n        this.drawer = false;\r\n        this.$refs.grid.query();\r\n        this.resetFormData();\r\n      }).catch(error => {\r\n        console.error('保存失败:', error);\r\n      }).finally(() => {\r\n        this.submitLoading = false;\r\n      });\r\n    },\r\n\r\n    // 表单取消处理\r\n    handleFormCancel() {\r\n      this.drawer = false;\r\n      this.resetFormData();\r\n    },\r\n\r\n    // 重置表单数据\r\n    resetFormData() {\r\n      // 使用浅拷贝替代深拷贝，提高性能\r\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\r\n      this.isEditMode = false;\r\n      this.submitLoading = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped></style>\r\n"], "mappings": ";;AAkLA,OAAAA,GAAA;AACA,OAAAC,IAAA;AACA,OAAAC,UAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,UAAA;AACA,IAAAC,iBAAA;EACAC,UAAA;EACAC,UAAA;EACAC,WAAA;EACAC,UAAA;AACA;AACA,IAAAC,iBAAA;EACA;EACA;EACA;EACA;EACA;AACA;AACA;EACAC,UAAA;IAAAV,IAAA,EAAAA,IAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAC,iBAAA,EAAAA;EAAA;EACAS,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA,CAAAC,IAAA;IACA,KAAAC,oBAAA,CAAAD,IAAA;;IAEA;IACA,SAAAE,YAAA;MACA,KAAAA,YAAA,CAAAC,UAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,KAAAL,cAAA,OAAAb,GAAA;IACA,KAAAe,oBAAA,OAAAf,GAAA;IACA;MACAmB,YAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,cAAA,EAAAC,MAAA,CAAAC,MAAA,KAAAd,iBAAA;MACAe,UAAA;MACAC,aAAA;MACAC,YAAA;MACAC,eAAA;MACAC,MAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,MAAA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,SAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,UAAA;MACAC,MAAA;MACAC,SAAA;MACAC,UAAA,EAAApB,MAAA,CAAAC,MAAA,KAAAnB,iBAAA;MACAuC,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GAEA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAI,IAAA;QACAF,OAAA;QACAC,QAAA;MACA,EACA;MACAE,SAAA;MACA;MACAC,kBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAA3B,YAAA;;IAEA;IACA,KAAA4B,SAAA;MACAC,UAAA;QACApD,UAAA,CAAAqD,eAAA;QACAH,KAAA,CAAA3B,YAAA;QACA+B,OAAA,CAAAC,GAAA;MACA;IACA;;IAEA;IACA,KAAA3C,YAAA,GAAAZ,UAAA,CAAAwD,gBAAA;EACA;EAEAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA5C,IAAA;MACA,KAAAgC,SAAA,GAAAhC,IAAA;MACA,KAAAS,YAAA;IACA;IAEAoC,qBAAA,WAAAA,sBAAAC,GAAA;MACA;MACAN,OAAA,CAAAC,GAAA,YAAAK,GAAA,CAAAzD,UAAA,EAAAyD,GAAA,CAAAvD,UAAA;MACA;MACA;IACA;IAEAwD,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAzB,MAAA;MACA;MACA,KAAAnB,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,iBAAA;MACA,KAAA8B,UAAA;MACA,KAAAf,UAAA;;MAEA;MACA,KAAA8B,SAAA;QACA,IAAAW,MAAA,CAAAC,KAAA,CAAAC,UAAA;UACAF,MAAA,CAAAC,KAAA,CAAAC,UAAA,CAAAC,SAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAN,GAAA;MAAA,IAAAO,MAAA;MACA;MACA,IAAAC,QAAA,GAAAjD,MAAA,CAAAC,MAAA,KAAAwC,GAAA;MACAN,OAAA,CAAAC,GAAA,cAAAK,GAAA;MACAN,OAAA,CAAAC,GAAA,oBAAAK,GAAA,CAAAvD,UAAA,SAAAgE,OAAA,CAAAT,GAAA,CAAAvD,UAAA;;MAEA;MACA,IAAA+D,QAAA,CAAA/D,UAAA,KAAAiE,SAAA,IAAAF,QAAA,CAAA/D,UAAA;QACA+D,QAAA,CAAA/D,UAAA;MACA,kBAAA+D,QAAA,CAAA/D,UAAA;QACA+D,QAAA,CAAA/D,UAAA,GAAA+D,QAAA,CAAA/D,UAAA;MACA,WAAA+D,QAAA,CAAA/D,UAAA,aAAA+D,QAAA,CAAA/D,UAAA,eAAA+D,QAAA,CAAA/D,UAAA;QACA+D,QAAA,CAAA/D,UAAA;MACA,WAAA+D,QAAA,CAAA/D,UAAA,cAAA+D,QAAA,CAAA/D,UAAA,gBAAA+D,QAAA,CAAA/D,UAAA;QACA+D,QAAA,CAAA/D,UAAA;MACA;QACA+D,QAAA,CAAA/D,UAAA;MACA;MAEAiD,OAAA,CAAAC,GAAA,YAAAa,QAAA;MACAd,OAAA,CAAAC,GAAA,qBAAAa,QAAA,CAAA/D,UAAA;MAEA,KAAAa,cAAA,GAAAkD,QAAA;MACA,KAAA/B,MAAA;MACA,KAAAD,UAAA;MACA,KAAAf,UAAA;;MAEA;MACA,KAAA8B,SAAA;QACA,IAAAgB,MAAA,CAAAJ,KAAA,CAAAC,UAAA;UACAG,MAAA,CAAAJ,KAAA,CAAAC,UAAA,CAAAC,SAAA;QACA;MACA;IACA;IACAM,iBAAA,WAAAA,kBAAAX,GAAA;MACA,KAAAZ,aAAA,GAAA7B,MAAA,CAAAC,MAAA,KAAAwC,GAAA;MACA,KAAAb,kBAAA;IACA;IACAyB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA;MACA,KAAAX,KAAA,CAAAY,aAAA,CAAAC,SAAA,CAAAC,OAAA,WAAAC,CAAA;QACAJ,GAAA,CAAAK,IAAA,CAAAD,CAAA,CAAAE,EAAA;MACA;MACA1B,OAAA,CAAA2B,IAAA,CAAAP,GAAA,OAAAX,KAAA,CAAAY,aAAA,CAAAC,SAAA;MACA,KAAAF,GAAA,CAAAQ,MAAA;QACA,KAAAC,QAAA,CAAAF,IAAA;QACA;MACA;;MAEA;MACA,KAAAG,QAAA,+CAAAC,MAAA,CAAAX,GAAA,CAAAQ,MAAA;QACAI,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAhB,MAAA,CAAAW,QAAA,yCAAAC,MAAA,CAAAX,GAAA,CAAAQ,MAAA;UACAI,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAC,IAAA;UACA;UACAhB,MAAA,CAAAiB,IAAA;YAAAhB,GAAA,EAAAA;UAAA,GAAAe,IAAA,WAAA3E,IAAA;YACA2D,MAAA,CAAAV,KAAA,CAAA4B,IAAA,CAAAC,KAAA;YACAnB,MAAA,CAAAU,QAAA;cACAnD,OAAA;cACAwD,IAAA;YACA;UACA,GAAAK,KAAA,WAAAC,KAAA;YACArB,MAAA,CAAAU,QAAA;cACAnD,OAAA;cACAwD,IAAA;YACA;UACA;QACA,GAAAK,KAAA;UACApB,MAAA,CAAAU,QAAA;YACAK,IAAA;YACAxD,OAAA;UACA;QACA;MACA,GAAA6D,KAAA;QACApB,MAAA,CAAAU,QAAA;UACAK,IAAA;UACAxD,OAAA;QACA;MACA;IACA;IACA+D,YAAA,WAAAA,aAAAjB,CAAA;MAAA,IAAAkB,MAAA;MACA;MACA,KAAAZ,QAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAO,MAAA,CAAAZ,QAAA;UACAE,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAC,IAAA;UACA;UACAO,MAAA,CAAAN,IAAA;YAAAhB,GAAA,GAAAI,CAAA;UAAA,GAAAW,IAAA,WAAA3E,IAAA;YACAkF,MAAA,CAAAjC,KAAA,CAAA4B,IAAA,CAAAC,KAAA;YACAI,MAAA,CAAAb,QAAA;cACAnD,OAAA;cACAwD,IAAA;YACA;UACA,GAAAK,KAAA,WAAAC,KAAA;YACAE,MAAA,CAAAb,QAAA;cACAnD,OAAA;cACAwD,IAAA;YACA;UACA;QACA,GAAAK,KAAA;UACAG,MAAA,CAAAb,QAAA;YACAK,IAAA;YACAxD,OAAA;UACA;QACA;MACA,GAAA6D,KAAA;QACAG,MAAA,CAAAb,QAAA;UACAK,IAAA;UACAxD,OAAA;QACA;MACA;IACA;IAEAiE,WAAA,WAAAA,YAAA;MACA,KAAAlC,KAAA,CAAA4B,IAAA,CAAAC,KAAA;IACA;IACAM,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA5D,UAAA,GAAApB,MAAA,CAAAC,MAAA,KAAAnB,iBAAA;MACA,KAAAkD,SAAA;QACAgD,MAAA,CAAApC,KAAA,CAAA4B,IAAA,CAAAC,KAAA;MACA;IACA;IACAQ,SAAA,WAAAA,UAAAtB,CAAA;MACA,KAAAtC,OAAA,GAAAsC,CAAA;IACA;IACAuB,gBAAA,WAAAA,iBAAAzC,GAAA;MACA,KAAA3C,YAAA,KAAA2C,GAAA,CAAA0C,UAAA;MACA,KAAApF,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAwC,GAAA;MACA,KAAA7C,YAAA;MACA,KAAAC,gBAAA;IACA;IACAuF,kBAAA,WAAAA,mBAAAzB,CAAA;MAAA,IAAA0B,MAAA;MACA,KAAAd,IAAA;QAAAhB,GAAA,GAAAI,CAAA;MAAA,GAAAW,IAAA,WAAA3E,IAAA;QACA0F,MAAA,CAAAzC,KAAA,CAAA0C,UAAA,CAAAb,KAAA;QACAY,MAAA,CAAArB,QAAA;UACAnD,OAAA;UACAwD,IAAA;QACA;MACA;IACA;IACA;IACAkB,gBAAA,WAAAA,iBAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAAtF,aAAA;MACA,KAAAoE,IAAA,uBAAAiB,QAAA,EAAAlB,IAAA,WAAA3E,IAAA;QACA8F,MAAA,CAAAzB,QAAA;UACAnD,OAAA;UACAwD,IAAA;QACA;QACAoB,MAAA,CAAAvE,MAAA;QACAuE,MAAA,CAAA7C,KAAA,CAAA4B,IAAA,CAAAC,KAAA;QACAgB,MAAA,CAAAC,aAAA;MACA,GAAAhB,KAAA,WAAAC,KAAA;QACAxC,OAAA,CAAAwC,KAAA,UAAAA,KAAA;MACA,GAAAgB,OAAA;QACAF,MAAA,CAAAtF,aAAA;MACA;IACA;IAEA;IACAyF,gBAAA,WAAAA,iBAAA;MACA,KAAA1E,MAAA;MACA,KAAAwE,aAAA;IACA;IAEA;IACAA,aAAA,WAAAA,cAAA;MACA;MACA,KAAA3F,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,iBAAA;MACA,KAAAe,UAAA;MACA,KAAAC,aAAA;IACA;EACA;AACA", "ignoreList": []}]}